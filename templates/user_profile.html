{% extends "base.html" %}

{% block title %}User Profile{% endblock %}

{% block page_title %}User Profile{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Profile</li>
{% endblock %}



{% block extra_css %}
<style>
    /* User Profile Modern Theme - CSS Variables Fallbacks */
    :root {
        --glass-bg-medium: rgba(255, 255, 255, 0.08);
        --glass-border-light: rgba(255, 255, 255, 0.3);
        --glass-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --glass-blur: blur(20px);
    }

    body.dark-mode {
        --glass-bg-medium: rgba(255, 255, 255, 0.08);
        --glass-border-light: rgba(255, 255, 255, 0.15);
        --glass-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
    }

    /* User Profile Modern Theme - Mobile First */
    .profile-content {
        min-height: calc(100vh - var(--topbar-height, 60px));
        padding: 0.5rem;
        position: relative;
    }

    .profile-card {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        box-shadow: var(--glass-shadow);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        margin-bottom: 1rem;
        padding: 1rem;
        width: 100%;
    }

    .profile-card:hover {
        transform: translateY(-1px);
        box-shadow: var(--glass-shadow-lg);
    }

    .profile-header {
        text-align: center;
        margin-bottom: 1rem;
        padding: 0.5rem;
    }

    .profile-name {
        font-family: 'Comfortaa', sans-serif;
        font-weight: 700;
        font-size: clamp(1.25rem, 5vw, 2rem);
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px var(--shadow-color);
        line-height: 1.2;
        word-break: break-word;
    }

    .profile-role {
        font-size: clamp(0.875rem, 3.5vw, 1.125rem);
        color: var(--text-secondary);
        font-weight: 500;
        margin-bottom: 0.5rem;
        line-height: 1.3;
    }

    .profile-address {
        font-size: clamp(0.75rem, 3vw, 1rem);
        color: var(--text-muted);
        font-weight: 400;
        line-height: 1.4;
        word-break: break-word;
    }

    .profile-info-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .profile-info-item {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 0.75rem;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
        min-height: auto;
    }

    .profile-info-item:hover {
        background: var(--glass-bg-medium);
        transform: translateY(-1px);
        border-color: var(--glass-border-light);
    }

    .info-label {
        font-size: clamp(0.75rem, 2.5vw, 0.875rem);
        font-weight: 600;
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-size: clamp(0.875rem, 3vw, 1rem);
        font-weight: 500;
        color: var(--text-primary);
        word-break: break-word;
        line-height: 1.4;
        width: 100%;
    }

    .profile-info-item .info-icon {
        width: clamp(16px, 4vw, 20px);
        height: clamp(16px, 4vw, 20px);
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--glass-bg);
        border-radius: 50%;
        font-size: clamp(0.625rem, 2vw, 0.75rem);
        color: var(--text-primary);
        flex-shrink: 0;
    }

    /* Ensure navbar icons maintain proper size */
    .topbar-actions .action-btn i {
        font-size: 1rem !important;
    }

    .edit-btn {
        background: rgba(99, 102, 241, 0.2);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid rgba(99, 102, 241, 0.3);
        border-radius: 12px;
        padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 4vw, 1.5rem);
        color: var(--text-primary);
        text-decoration: none;
        font-weight: 600;
        font-size: clamp(0.75rem, 2.5vw, 0.875rem);
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1rem;
        width: 100%;
        max-width: 200px;
    }

    .edit-btn:hover {
        background: rgba(99, 102, 241, 0.3);
        color: var(--text-primary);
        transform: translateY(-1px);
        box-shadow: var(--glass-shadow);
        text-decoration: none;
    }


    /* QR Code Section */
    .qr-section {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 1rem;
        text-align: center;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .qr-section:hover {
        transform: translateY(-1px);
        box-shadow: var(--glass-shadow-lg);
    }

    .qr-title {
        font-family: 'Comfortaa', sans-serif;
        font-weight: 700;
        font-size: clamp(1rem, 4vw, 1.5rem);
        color: var(--text-primary);
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px var(--shadow-color);
        line-height: 1.2;
    }

    .qr-image {
        background: var(--bg-primary);
        border-radius: 12px;
        padding: 0.75rem;
        display: inline-block;
        margin-bottom: 1rem;
        box-shadow: var(--glass-shadow);
        max-width: 100%;
        width: fit-content;
    }

    .qr-image img {
        border-radius: 8px;
        max-width: 100%;
        width: clamp(150px, 40vw, 200px);
        height: auto;
        display: block;
    }

    .qr-description {
        color: var(--text-secondary);
        margin-bottom: 1rem;
        font-size: clamp(0.75rem, 3vw, 1rem);
        line-height: 1.4;
        padding: 0 0.5rem;
    }

    .download-btn {
        background: rgba(16, 185, 129, 0.2);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid rgba(16, 185, 129, 0.3);
        border-radius: 12px;
        padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 4vw, 1.5rem);
        color: var(--text-primary);
        font-weight: 600;
        font-size: clamp(0.75rem, 2.5vw, 0.875rem);
        transition: all 0.3s ease;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        width: 100%;
        max-width: 200px;
        margin: 0 auto;
    }

    .download-btn:hover {
        background: rgba(16, 185, 129, 0.3);
        transform: translateY(-1px);
        box-shadow: var(--glass-shadow);
    }

    /* Transaction History */
    .transaction-section {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .transaction-section:hover {
        transform: translateY(-1px);
        box-shadow: var(--glass-shadow-lg);
    }

    .transaction-header {
        background: var(--glass-bg);
        padding: 1rem;
        border-bottom: 1px solid var(--glass-border);
    }

    .transaction-title {
        font-family: 'Comfortaa', sans-serif;
        font-weight: 700;
        font-size: clamp(1rem, 4vw, 1.25rem);
        color: var(--text-primary);
        margin: 0;
        text-shadow: 0 2px 4px var(--shadow-color);
        line-height: 1.2;
    }

    .transaction-table {
        background: transparent;
        color: var(--text-primary);
        width: 100%;
        min-width: 400px;
        font-size: clamp(0.75rem, 2.5vw, 0.875rem);
        border-collapse: collapse;
    }

    .transaction-table th {
        background: var(--glass-bg);
        color: var(--text-primary);
        border: none;
        padding: clamp(0.5rem, 2vw, 0.75rem);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: clamp(0.625rem, 2vw, 0.75rem);
        white-space: nowrap;
    }

    .transaction-table td {
        background: transparent;
        color: var(--text-secondary);
        border: none;
        border-bottom: 1px solid var(--glass-border);
        padding: clamp(0.5rem, 2vw, 0.75rem);
        font-weight: 500;
        font-size: clamp(0.75rem, 2.5vw, 0.875rem);
        word-break: break-word;
    }

    .transaction-table tbody tr:hover {
        background: var(--glass-bg);
    }

    .status-credit {
        color: #10b981;
        font-weight: 600;
    }

    .status-debit {
        color: #ef4444;
        font-weight: 600;
    }

    /* Sub-Librarian Data Table */
    .data-table-section {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .data-table-section:hover {
        transform: translateY(-1px);
        box-shadow: var(--glass-shadow-lg);
    }

    .data-table-header {
        background: var(--glass-bg);
        padding: 1rem;
        border-bottom: 1px solid var(--glass-border);
    }

    .data-table-title {
        font-family: 'Comfortaa', sans-serif;
        font-weight: 700;
        font-size: clamp(1rem, 4vw, 1.25rem);
        color: var(--text-primary);
        margin: 0;
        text-shadow: 0 2px 4px var(--shadow-color);
        line-height: 1.2;
    }

    .data-table-body {
        padding: 0.5rem;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .modern-table {
        background: transparent;
        color: var(--text-primary);
        border-radius: 8px;
        overflow: hidden;
        width: 100%;
        min-width: 500px;
        font-size: clamp(0.75rem, 2.5vw, 0.875rem);
        border-collapse: collapse;
    }

    .modern-table th {
        background: var(--glass-bg);
        color: var(--text-primary);
        border: none;
        padding: clamp(0.5rem, 2vw, 0.75rem);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: clamp(0.625rem, 2vw, 0.75rem);
        white-space: nowrap;
    }

    .modern-table td {
        background: transparent;
        color: var(--text-secondary);
        border: none;
        border-bottom: 1px solid var(--glass-border);
        padding: clamp(0.5rem, 2vw, 0.75rem);
        font-weight: 500;
        font-size: clamp(0.75rem, 2.5vw, 0.875rem);
        word-break: break-word;
    }

    .modern-table tbody tr:hover {
        background: var(--glass-bg);
    }

    .action-btn {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 6px;
        padding: clamp(0.25rem, 1vw, 0.5rem);
        color: var(--text-primary);
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: clamp(24px, 6vw, 32px);
        height: clamp(24px, 6vw, 32px);
        margin: 0 0.125rem;
        font-size: clamp(0.625rem, 2vw, 0.75rem);
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
        transform: scale(1.05);
    }

    .action-btn.edit {
        background: rgba(16, 185, 129, 0.2);
        border-color: rgba(16, 185, 129, 0.3);
    }

    .action-btn.delete {
        background: rgba(239, 68, 68, 0.2);
        border-color: rgba(239, 68, 68, 0.3);
    }

    /* Responsive Breakpoints - Mobile First */

    /* Small devices (landscape phones, 576px and up) */
    @media (min-width: 576px) {
        .profile-content {
            padding: 0.75rem;
        }

        .profile-card {
            padding: 1.25rem;
            border-radius: 16px;
        }

        .profile-info-item {
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }

        .info-value {
            text-align: right;
            margin-left: auto;
        }

        .edit-btn {
            width: auto;
            max-width: none;
        }
    }

    /* Medium devices (tablets, 768px and up) */
    @media (min-width: 768px) {
        .profile-content {
            padding: 1rem;
        }

        .profile-card {
            padding: 1.5rem;
            border-radius: 20px;
        }

        .qr-section,
        .transaction-section,
        .data-table-section {
            border-radius: 16px;
        }

        .data-table-body {
            padding: 1rem;
        }

        .modern-table {
            min-width: 600px;
        }
    }

    /* Large devices (desktops, 992px and up) */
    @media (min-width: 992px) {
        .profile-content {
            padding: 1.5rem;
            padding-bottom: 1.5rem;
        }

        .profile-card {
            margin-bottom: 1.5rem;
        }

        .qr-section,
        .transaction-section,
        .data-table-section {
            margin-bottom: 1.5rem;
            border-radius: 20px;
        }
    }

    /* Extra large devices (large desktops, 1200px and up) */
    @media (min-width: 1200px) {
        .profile-content {
            padding: 2rem;
        }

        .profile-card {
            padding: 2rem;
        }

        .qr-section {
            padding: 2rem;
        }

        .transaction-header,
        .data-table-header {
            padding: 1.5rem;
        }

        .data-table-body {
            padding: 1.5rem;
        }
    }

    /* Mobile specific adjustments */
    @media (max-width: 991.98px) {
        .profile-content {
            padding-bottom: calc(0.5rem + var(--bottom-menu-height, 80px));
        }
    }

    /* Ultra small devices */
    @media (max-width: 375px) {
        .profile-content {
            padding: 0.25rem;
        }

        .profile-card {
            padding: 0.75rem;
            border-radius: 12px;
        }

        .modern-table {
            min-width: 400px;
        }

        .qr-image img {
            width: clamp(120px, 35vw, 150px);
        }
    }

    /* Animation for page load */
    .profile-card,
    .qr-section,
    .transaction-section,
    .data-table-section {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* DataTables Custom Styling */
    .dataTables_wrapper {
        color: var(--text-primary);
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        color: var(--text-secondary);
    }

    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        color: var(--text-primary);
        padding: 0.5rem;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        color: var(--text-primary) !important;
        margin: 0 0.25rem;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: var(--glass-border) !important;
        color: var(--text-primary) !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: rgba(99, 102, 241, 0.3) !important;
        border-color: rgba(99, 102, 241, 0.5) !important;
        color: var(--text-primary) !important;
    }

    /* Dark Mode Specific Enhancements */
    body.dark-mode .profile-card:hover {
        box-shadow: var(--glass-shadow-lg);
        border-color: var(--glass-border-light);
    }

    body.dark-mode .qr-image {
        background: var(--bg-primary);
        box-shadow: var(--glass-shadow);
    }

    body.dark-mode .transaction-table tbody tr:hover,
    body.dark-mode .modern-table tbody tr:hover {
        background: var(--glass-bg-medium);
    }

    /* Ensure proper text contrast in all modes */
    .profile-name,
    .qr-title,
    .transaction-title,
    .data-table-title {
        color: var(--text-primary) !important;
    }

    .profile-role,
    .qr-description {
        color: var(--text-secondary) !important;
    }

    .profile-address {
        color: var(--text-muted) !important;
    }

    /* Table scroll indicators */
    .table-scroll-wrapper {
        position: relative;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
        scrollbar-color: var(--glass-border) transparent;
    }

    .table-scroll-wrapper::-webkit-scrollbar {
        height: 6px;
    }

    .table-scroll-wrapper::-webkit-scrollbar-track {
        background: var(--glass-bg);
        border-radius: 3px;
    }

    .table-scroll-wrapper::-webkit-scrollbar-thumb {
        background: var(--glass-border);
        border-radius: 3px;
    }

    .table-scroll-wrapper::-webkit-scrollbar-thumb:hover {
        background: var(--glass-border-light);
    }

    /* Mobile optimizations */
    @media (max-width: 575.98px) {
        .profile-info-item {
            padding: 0.75rem;
        }

        .info-label,
        .info-value {
            font-size: 0.875rem;
        }

        .edit-btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .transaction-table,
        .modern-table {
            min-width: 350px;
        }

        .table-scroll-wrapper {
            margin: 0 -0.5rem;
            padding: 0 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-content">
    <div class="container-fluid px-1 px-sm-2 px-md-3 px-lg-4">
        <!-- Messages -->
        {% if messages %}
        <div id="messageContainer" class="mb-4">
            {% for message in messages %}
            {% if message.tags == 'success' %}
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="background: rgba(16, 185, 129, 0.2); border: 1px solid rgba(16, 185, 129, 0.3); color: white;">
                {% elif message.tags == 'error' %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert" style="background: rgba(239, 68, 68, 0.2); border: 1px solid rgba(239, 68, 68, 0.3); color: white;">
                    {% elif message.tags == 'warning' %}
                    <div class="alert alert-warning alert-dismissible fade show" role="alert" style="background: rgba(245, 158, 11, 0.2); border: 1px solid rgba(245, 158, 11, 0.3); color: white;">
                        {% elif message.tags == 'info' %}
                        <div class="alert alert-info alert-dismissible fade show" role="alert" style="background: rgba(59, 130, 246, 0.2); border: 1px solid rgba(59, 130, 246, 0.3); color: white;">
                            {% else %}
                            <div class="alert alert-secondary alert-dismissible fade show" role="alert" style="background: rgba(107, 114, 128, 0.2); border: 1px solid rgba(107, 114, 128, 0.3); color: white;">
                                {% endif %}
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close" style="filter: invert(1);"></button>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}


        <div class="row g-2 g-sm-3 g-lg-4">
            <div class="col-12 col-md-6 col-lg-5 col-xl-4">
                <!-- Profile Summary Card -->
                <div class="profile-card">
                    <div class="profile-header">
                        {% if role == 'librarian' %}
                            <h1 class="profile-name" id="profileName">{{lib.user.first_name}} {{lib.user.last_name}}</h1>
                            <p class="profile-role">{{lib.library_name}}</p>
                            <p class="profile-address" id="profileAddress">{{lib.librarian_address}}</p>
                        {% elif role == 'sublibrarian' %}
                            <h1 class="profile-name" id="profileName">{{superlib.user.first_name}} {{superlib.user.last_name}}</h1>
                            <p class="profile-role">{{lib.librarian.library_name}}</p>
                            <p class="profile-address" id="profileAddress">{{lib.librarian.librarian_address}}</p>
                        {% elif role == 'librarycommander' %}
                            <h1 class="profile-name" id="profileName">{{lib.user.first_name}} {{lib.user.last_name}}</h1>
                            <p class="profile-role">Library Commander</p>
                        {% elif role == 'manager' %}
                            <h1 class="profile-name" id="profileName">{{lib.user.first_name}} {{lib.user.last_name}}</h1>
                            <p class="profile-role">Manager</p>
                        {% endif %}

                        {% if role == 'librarian' or role == 'librarycommander' or role == 'manager' %}
                            <a href="/{{role}}/edit-profile{% if role == 'librarian' %}/{% endif %}" class="edit-btn">
                                <i class="fas fa-edit"></i>
                                Edit Profile
                            </a>
                        {% endif %}
                    </div>
                </div>
                    {% comment %} <div class="card profile_section_card rounded-3 mb-4 mb-lg-0" style="background-color: #f8f9fa; padding: 10px 15px; border-bottom: 1px solid #ddd;">
                        <div class="card-body p-0">
                            <ul class="list-group list-group-flush rounded-3">
                                <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                    <i class="fas fa-globe fa-lg text-warning"></i>
                                    <p class="mb-0">https://mdbootstrap.com</p>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                    <i class="fab fa-github fa-lg text-body"></i>
                                    <p class="mb-0">mdbootstrap</p>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                    <i class="fab fa-twitter fa-lg" style="color: #55acee;"></i>
                                    <p class="mb-0">@mdbootstrap</p>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                    <i class="fab fa-instagram fa-lg" style="color: #ac2bac;"></i>
                                    <p class="mb-0">mdbootstrap</p>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                    <i class="fab fa-facebook-f fa-lg" style="color: #3b5998;"></i>
                                    <p class="mb-0">mdbootstrap</p>
                                </li>
                            </ul>
                        </div>
                    </div> {% endcomment %}
<!-- for the QR -->

<!-- QR end                -->

                {% if role == 'librarian' %}
                <!-- QR Code Section -->
                <div class="qr-section">
                    <h2 class="qr-title">QR Code for {{ lib.library_name }}</h2>
                    <div class="qr-image">
                        <img id="qrImage" src="data:image/png;base64,{{ qr_code }}" alt="QR Code" class="img-fluid">
                    </div>
                    <p class="qr-description">Scan the QR code above to view the library details.</p>
                    <button class="download-btn" onclick="downloadQR()">
                        <i class="fas fa-download"></i>
                        Download QR Code
                    </button>
                </div>



                <!-- Transaction History -->
                <div class="transaction-section">
                    <div class="transaction-header">
                        <h3 class="transaction-title">Transaction History</h3>
                    </div>
                    <div class="table-scroll-wrapper">
                        <table class="transaction-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Description</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for data in transaction %}
                                <tr>
                                    <td>{{data.created_at|date:"d-m-Y"}}</td>
                                    <td>{{data.note}}</td>
                                    <td>₹{{data.amount}}</td>
                                    <td>
                                        {% if data.is_credit %}
                                            <span class="status-credit">
                                                <i class="fas fa-arrow-up"></i> Credit
                                            </span>
                                        {% else %}
                                            <span class="status-debit">
                                                <i class="fas fa-arrow-down"></i> Debit
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

            </div>
            <div class="col-12 col-md-6 col-lg-7 col-xl-8">
                <!-- Detailed Profile Information -->
                <div class="profile-card">
                    {% if role == 'librarian' %}
                    <div class="profile-info-grid">
                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                Library Name
                            </div>
                            <div class="info-value" id="libraryName">{{lib.library_name}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                Full Name
                            </div>
                            <div class="info-value" id="fullName">{{lib.user.first_name}} {{lib.user.last_name}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                Email
                            </div>
                            <div class="info-value" id="email">{{lib.user.email}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                Mobile
                            </div>
                            <div class="info-value" id="mobile">{{lib.librarian_phone_num}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                Address
                            </div>
                            <div class="info-value" id="address">{{lib.librarian_address}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-map"></i>
                                </div>
                                Google Map
                            </div>
                            <div class="info-value">
                                <a href="{{lib.google_map_url}}" target="_blank" style="color: rgba(255, 255, 255, 0.8); text-decoration: underline;">
                                    View Location
                                </a>
                            </div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                Membership
                            </div>
                            <div class="info-value">{{membership.plan.name}} | ₹{{membership.plan.price}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-calendar"></i>
                                </div>
                                Membership Period
                            </div>
                            <div class="info-value">{{membership.start_date}} to {{membership.expiry_date}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-wallet"></i>
                                </div>
                                Wallet Balance
                            </div>
                            <div class="info-value">
                                <i class="fa-solid fa-coins" style="color: #fbbf24;"></i> {{wallet.balance}} Points
                            </div>
                        </div>

                        <!-- Registration Settings -->
                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                Security Deposit
                            </div>
                            <div class="info-value">
                                {% if lib.security_deposit_enabled %}
                                    <span style="color: #28a745;">
                                        <i class="fas fa-check-circle"></i>
                                        Enabled (₹{{ lib.security_deposit_amount }})
                                    </span>
                                {% else %}
                                    <span style="color: #6c757d;">
                                        <i class="fas fa-times-circle"></i>
                                        Disabled
                                    </span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-id-card"></i>
                                </div>
                                Proof of Identity
                            </div>
                            <div class="info-value">
                                {% if lib.proof_of_identity_enabled %}
                                    <span style="color: #28a745;">
                                        <i class="fas fa-check-circle"></i>
                                        Required
                                    </span>
                                {% else %}
                                    <span style="color: #6c757d;">
                                        <i class="fas fa-times-circle"></i>
                                        Not Required
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% elif role == 'sublibrarian' %}
                    <div class="profile-info-grid">
                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                Library Name
                            </div>
                            <div class="info-value">{{lib.librarian.library_name}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                Full Name
                            </div>
                            <div class="info-value" id="fullName">{{lib.user.first_name}} {{lib.user.last_name}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                Email
                            </div>
                            <div class="info-value" id="email">{{lib.user.email}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                Mobile
                            </div>
                            <div class="info-value" id="mobile">{{lib.sublibrarian_phone_num}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                Address
                            </div>
                            <div class="info-value" id="address">{{lib.sublibrarian_address}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                Membership
                            </div>
                            <div class="info-value">{{membership.plan.name}} | ₹{{membership.plan.price}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-calendar"></i>
                                </div>
                                Membership Period
                            </div>
                            <div class="info-value">{{membership.start_date}} to {{membership.expiry_date}}</div>
                        </div>
                    </div>
                            {% comment %} <div class="d-flex justify-content-center mt-4 mb-2">
                                <a href="/{{role}}/edit-profile/" class="btn btn-dark custom-margin">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </div> {% endcomment %}


                    {% elif role == 'librarycommander' %}
                    <div class="profile-info-grid">
                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                Full Name
                            </div>
                            <div class="info-value" id="fullName">{{lib.user.first_name}} {{lib.user.last_name}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                Email
                            </div>
                            <div class="info-value" id="email">{{lib.user.email}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                Mobile
                            </div>
                            <div class="info-value" id="mobile">{{lib.librarycommander_phone_num}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                Address
                            </div>
                            <div class="info-value" id="address">{{lib.librarycommander_address}}</div>
                        </div>
                    </div>

                    {% elif role == 'manager' %}
                    <div class="profile-info-grid">
                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                Full Name
                            </div>
                            <div class="info-value" id="fullName">{{lib.user.first_name}} {{lib.user.last_name}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                Email
                            </div>
                            <div class="info-value" id="email">{{lib.user.email}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                Mobile
                            </div>
                            <div class="info-value" id="mobile">{{lib.manager_phone_num}}</div>
                        </div>

                        <div class="profile-info-item">
                            <div class="info-label">
                                <div class="info-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                Address
                            </div>
                            <div class="info-value" id="address">{{lib.manager_address}}</div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                {% if role == 'librarian' %}
                <!-- Sub-Librarian Data Table -->
                <div class="data-table-section">
                    <div class="data-table-header">
                        <h3 class="data-table-title">Sub-Librarian Data</h3>
                    </div>
                    <div class="data-table-body">
                        <div class="table-scroll-wrapper">
                            <table id="basic-datatables" class="modern-table display table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Phone Number</th>
                                        <th>Email</th>
                                        <th>Address</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for data in sublibrarian %}
                                    <tr>
                                        <td>{{ data.user.first_name }} {{ data.user.last_name }}</td>
                                        <td>{{ data.sublibrarian_phone_num }}</td>
                                        <td>{{ data.user.email }}</td>
                                        <td>{{ data.sublibrarian_address }}</td>
                                        <td>
                                            <a href="/librarian/sublibrarian/update/{{data.id}}" class="action-btn edit" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="/librarian/sublibrarian/delete/{{data.id}}" class="action-btn delete" title="Delete">
                                                <i class="fas fa-trash-alt"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}
                                <!-- <div class="container d-md-none">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group d-flex align-items-center">
                                                <label for="entries" class="mr-2 mb-0">Show entries:</label>
                                                <select id="entries" class="form-control form-control-sm w-auto">
                                                    <option value="5">5</option>
                                                    <option value="10">10</option>
                                                    <option value="25">25</option>
                                                    <option value="50">50</option>
                                                    <option value="100">100</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group d-flex align-items-center">
                                                <label for="search" class="mr-2 mb-0">Search:</label>
                                                <input type="text" id="search" class="form-control form-control-sm w-auto">
                                            </div>
                                        </div>
                                    </div>

                                    <div id="studentCards" class="student-cards ">
                                        {% for data in sublibrarian %}
                                        <div class="card mb-3 student-card status-{{ s.color|default:'normal' }}">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">{{ data.user.first_name }} {{ data.user.last_name }}</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <p class="card-text">
                                                            <i class="fas fa-book mr-2"></i>
                                                            <strong>Phone Number:</strong> {{ data.sublibrarian_phone_num }}
                                                        </p>
                                                        <p class="card-text">
                                                            <i class="fas fa-phone mr-2"></i>
                                                            <strong>Email:</strong> {{ data.user.email }}
                                                        </p>
                                                        <p class="card-text">
                                                            <i class="fas fa-address-card mr-2"></i>
                                                            <strong>Address:</strong> {{ data.sublibrarian_address }}
                                                        </p>
                                                        <p class="card-text">
                                                            <i class="fas fa-cogs mr-2"></i>
                                                            <strong>Action:</strong>
                                                             Edit Button 
                                                            <a href="/librarian/sublibrarian/update/{{data.id}}" class="btn btn-success btn-sm ml-2">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                             Delete Button 
                                                            <a href="/librarian/sublibrarian/delete/{{data.id}}" class="btn btn-danger btn-sm ml-2">
                                                                <i class="fas fa-trash-alt"></i>
                                                            </a>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                        </div> -->
                                        {% endfor %}
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <nav aria-label="Page navigation">
                                                <ul class="pagination justify-content-center">
                                                    <!-- Pagination will be dynamically generated -->
                                                </ul>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // User Profile Page Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize page animations
        initializeAnimations();

        // Initialize DataTables if present
        if (document.getElementById('basic-datatables')) {
            initializeDataTable();
        }

        // Removed page load success message
    });

    function initializeAnimations() {
        // Add staggered animation to profile cards
        const profileCards = document.querySelectorAll('.profile-card, .qr-section, .transaction-section, .data-table-section');
        profileCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.2}s`;
        });
    }

    function initializeDataTable() {
        $("#basic-datatables").DataTable({
            "pageLength": 5,
            "lengthMenu": [5, 10, 25, 50, 75, 100],
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "responsive": true,
            "language": {
                "search": "Search:",
                "lengthMenu": "Show _MENU_ entries",
                "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            }
        });
    }

    function downloadQR() {
        const qrImage = document.getElementById('qrImage');
        if (qrImage) {
            const link = document.createElement('a');
            link.href = qrImage.src;
            link.download = 'library_qr_code.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Show success toast
            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Download Started',
                    'QR code download has been initiated.',
                    'success'
                );
            }
        }
    }
    // Auto-hide alerts after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                alert.style.transition = 'opacity 1s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.style.display = 'none';
                }, 1000);
            }, 5000);
        });
    });
</script>
{% endblock %}