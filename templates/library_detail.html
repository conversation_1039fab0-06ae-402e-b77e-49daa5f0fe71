<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <link rel="icon" href="/static/img/librainian-logo-black-transparent.png" type="image/x-icon">

  <!-- Include Navbar Head Section -->
  {% with navbar_section="head" %}
      {% include "public_navbar.html" %}
  {% endwith %}

  <!-- Primary Meta Tags -->
  <title>{{library.library_name}} | Library Details, Hours & Contact | Librainian</title>
  <meta name="robots" content="index, follow, max-image-preview:large">
  <meta name="description" content="Discover {{library.library_name}}, a premier library in {{library.librarian_address}} offering excellent study spaces and reading facilities. View detailed information about opening hours, contact details, and available services. Register easily as a student with our convenient QR code system. {% if library.discount_available %}Special student discount of {{library.discount_amount}}% available for a limited time!{% endif %} Find the perfect environment for studying, research, and academic excellence.">
  <meta http-equiv="content-language" content="en">
  <meta name="keywords" content="{{library.library_name}}, best library in {{library.librarian_address}}, study space near {{library.librarian_address}}, {{library.library_name}} the quiet reading room, {{library.library_name}}'s opening hours, {{library.library_name}}'s contact information, {{library.library_name}}'s online registration form, {{library.library_name}} registration,{{library.library_name}} uses Librainian app, {{library.library_name}} location map, {{library.library_name}} contact number, {{library.library_name}} working hours, {{library.library_name}} timings,  library near me, top rated libraries in {{library.librarian_address}}, academic library, public library services, digital library access, library facilities, library amenities, library seating capacity, library membership fees,{{library.library_name}} for best at student services, {{library.library_name}} for exam preparation space, {{library.library_name}} reviews, {{library.library_name}} ratings{% if library.discount_available %}, find your library with librainian to get special library discount, librainian special library offers, Librainian discounted library membership, {{library.discount_amount}}% off library services, student discount in {{library.librarian_address}}{% endif %}">
  <meta name="geo.region" content="IN">
  <meta name="author" content="Librainian">
  <link rel="canonical" href="https://www.librainian.com/librarian/library-details/{{ library.slug }}/">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Librainian">
  <meta property="og:title" content="{{library.library_name}} | Library Details & Information">
  <meta property="og:description" content="Discover {{library.library_name}}, a premier library in {{library.librarian_address}} offering excellent study spaces and reading facilities. View detailed information about opening hours, contact details, and available services. {% if library.discount_available %}Special student discount of {{library.discount_amount}}% available for a limited time!{% endif %}">
  <meta property="og:url" content="https://www.librainian.com/librarian/library-details/{{ library.slug }}/">
  <meta property="og:image" content="{% if library.image %}{{ library.image.url }}{% else %}https://www.librainian.com/static/img/library-default.jpg{% endif %}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="en_US">

  <!-- Twitter -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:site" content="@librainian_app">
  <meta name="twitter:creator" content="@librainian_app">
  <meta name="twitter:title" content="{{library.library_name}} | Library Details & Information">
  <meta name="twitter:description" content="Discover {{library.library_name}}, a premier library in {{library.librarian_address}} offering excellent study spaces and reading facilities. View detailed information about opening hours, contact details, and available services. {% if library.discount_available %}Special student discount of {{library.discount_amount}}% available!{% endif %}">
  <meta name="twitter:image" content="{% if library.image %}{{ library.image.url }}{% else %}https://www.librainian.com/static/img/library-default.jpg{% endif %}">

<!-- Structured Data - Library Information -->
<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "{{ library.library_name }}",
    "image": "{% if library.image %}{{ library.image.url }}{% else %}https://www.librainian.com/static/img/library-default.jpg{% endif %}",
    "description": "{{ library.description|escapejs }}",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "{{ library.librarian_address }}",
      "addressCountry": "IN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 20.5937,
      "longitude": 78.9629
    },
    "url": "https://www.librainian.com/librarian/library-details/{{ library.slug }}/",
    "telephone": "{{ library.librarian_phone_num }}",
    "email": "{{ library.user.email }}",
    "openingHoursSpecification": [
      {% for shift in shifts %}
      {
        "@type": "OpeningHoursSpecification",
        "name": "{{ shift.name }}",
        "dayOfWeek": [
          "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"
        ],
        "opens": "{{ shift.opens }}",
        "closes": "{{ shift.closes }}",
        "priceSpecification": {
          "@type": "UnitPriceSpecification",
          "price": "{{ shift.rate }}",
          "priceCurrency": "INR",
          "description": "Fee for {{ shift.name }} shift"
        }
      }{% if not forloop.last %},{% endif %}
      {% endfor %}
    ],
    "sameAs": [
      "https://www.librainian.com/",
      "https://www.facebook.com/librainian",
      "https://twitter.com/librainian_app"
    ]
  }
  </script>
  

  <!-- Breadcrumb Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://www.librainian.com/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Libraries",
        "item": "https://www.librainian.com/librarian/library-list/"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": "{{library.library_name}}",
        "item": "https://www.librainian.com/librarian/library-details/{{ library.slug }}/"
      }
    ]
  }
  </script>

  <!-- Contact Form Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact {{library.library_name}}",
    "description": "Contact form to reach out to {{library.library_name}} for inquiries and information",
    "mainEntity": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "telephone": "{{library.librarian_phone_num}}",
      "email": "{{library.user.email}}",
      "areaServed": "{{library.librarian_address}}",
      "availableLanguage": ["English", "Hindi"]
    }
  }
  </script>


  <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

       <!-- Disable Right click -->

         

    <!-- disable Print Screen for Windows -->

      

    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
  <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-color: #1e293b;
            --text-primary: #1e293b;
            --text-light: #6b7280;
            --text-white: #ffffff;
            --glass-bg: rgba(255, 255, 255, 0.15);
            --glass-border: rgba(255, 255, 255, 0.25);
            --glass-backdrop: blur(16px);
            --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 10px 20px rgba(0, 0, 0, 0.2);
            --shadow-glass: 0 8px 32px rgba(31, 38, 135, 0.37);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Dark mode variables */
        body.dark-mode {
            --primary-color: #60a5fa;
            --primary-dark: #3b82f6;
            --secondary-color: #f3f4f6;
            --text-color: #f3f4f6;
            --text-primary: #f3f4f6;
            --text-light: #d1d5db;
            --glass-bg: rgba(0, 0, 0, 0.2);
            --glass-border: rgba(255, 255, 255, 0.1);
            --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        * {
            box-sizing: border-box;
        }

        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--text-primary);
            transition: var(--transition);
        }

        /* Dark mode body */
        body.dark-mode {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        }

        /* Top Controls */
        .top-controls {
            position: absolute;
            top: 1rem;
            left: 1rem;
            right: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .back-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            text-decoration: none;
            color: var(--text-white);
            font-weight: 500;
            transition: var(--transition);
            border-radius: 50px;
        }

        .back-button:hover {
            color: var(--text-white);
            text-decoration: none;
        }

        .dark-mode-toggle {
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-white);
            font-size: 1.1rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .dark-mode-toggle:hover {
            transform: scale(1.1);
        }

        /* Mobile adjustments for top controls */
        @media (max-width: 768px) {
            .top-controls {
                top: 0.5rem;
                left: 0.5rem;
                right: 0.5rem;
            }

            .back-button {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }

            .back-button span {
                display: none;
            }

            .dark-mode-toggle {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }

        /* Main container with transparent background, border radius and white border */
        .main-container {
            background: transparent;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 1rem;
            box-shadow: none;
            margin: 2rem auto;
            max-width: 1200px;
            position: relative;
            overflow: hidden;
        }

        /* Dark mode main container border */
        body.dark-mode .main-container {
            border: 2px solid rgba(255, 255, 255, 0.2);
        }



        /* Header section with glassmorphism effect */
        .header-section {
            background: rgba(59, 130, 246, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-top: none;
            border-left: none;
            border-right: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-white);
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
            z-index: 1;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
        }

        /* Dark mode header adjustments */
        body.dark-mode .header-section {
            background: rgba(59, 130, 246, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .header-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: rgba(255, 255, 255, 1);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header-section .subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 1.5rem;
        }

        .discount-badge {
            background: var(--warning-color);
            color: var(--text-white);
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: var(--shadow-sm);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Content sections */
        .content-section {
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        /* Glass Card Classes */
        .glass-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.05) inset;
            transition: var(--transition-slow);
            position: relative;
            overflow: hidden;
        }

        .glass-card:hover {
            transform: translateY(-3px);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
        }

        /* Light mode glass card */
        body:not(.dark-mode) .glass-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.12),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        }

        body:not(.dark-mode) .glass-card:hover {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset,
                0 2px 8px rgba(255, 255, 255, 0.2) inset;
        }

        /* Dark mode glass card */
        body.dark-mode .glass-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.05) inset;
        }

        body.dark-mode .glass-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 2px 8px rgba(255, 255, 255, 0.05) inset;
        }

        .glass-body {
            background: transparent;
            color: var(--text-primary);
        }

        .glass-header {
            background: rgba(255, 255, 255, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .info-card, .contact-card, .qr-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.05) inset;
            transition: var(--transition-slow);
            position: relative;
        }

        /* Enhanced glass transparency for light mode */
        body:not(.dark-mode) .info-card,
        body:not(.dark-mode) .contact-card,
        body:not(.dark-mode) .qr-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.12),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 2px 4px rgba(255, 255, 255, 0.1) inset;
        }

        /* Dark mode card styling */
        body.dark-mode .info-card,
        body.dark-mode .contact-card,
        body.dark-mode .qr-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.05) inset;
        }

        .info-card:hover, .contact-card:hover, .qr-card:hover {
            transform: translateY(-5px);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
        }

        /* Light mode hover effects */
        body:not(.dark-mode) .info-card:hover,
        body:not(.dark-mode) .contact-card:hover,
        body:not(.dark-mode) .qr-card:hover {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset,
                0 2px 8px rgba(255, 255, 255, 0.2) inset;
        }

        /* Dark mode hover effects */
        body.dark-mode .info-card:hover,
        body.dark-mode .contact-card:hover,
        body.dark-mode .qr-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 2px 8px rgba(255, 255, 255, 0.05) inset;
        }

        .info-card h2, .contact-card h2, .qr-card h2 {
            color: var(--text-primary);
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Dark mode text adjustments */
        body.dark-mode .glass-body {
            color: var(--text-primary);
        }

        body.dark-mode .glass-header {
            background: rgba(255, 255, 255, 0.05);
            border-top-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .glass-card:hover {
            box-shadow:
                0 35px 60px -12px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 2px 4px rgba(255, 255, 255, 0.1) inset;
        }

        /* Text visibility improvements for light mode */
        .info-item h3,
        .info-item p,
        .card-text,
        .form-label,
        .subtitle,
        .text-muted {
            color: var(--text-primary);
        }

        /* Light mode specific text improvements - using white text */
        body:not(.dark-mode) .info-item h3,
        body:not(.dark-mode) .info-item p,
        body:not(.dark-mode) .card-text,
        body:not(.dark-mode) .form-label,
        body:not(.dark-mode) .subtitle,
        body:not(.dark-mode) .text-muted,
        body:not(.dark-mode) p,
        body:not(.dark-mode) span {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Light mode card text visibility - using white text */
        body:not(.dark-mode) .contact-card p,
        body:not(.dark-mode) .qr-card p,
        body:not(.dark-mode) .info-card p {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Light mode badge visibility */
        body:not(.dark-mode) .badge {
            color: rgba(255, 255, 255, 1) !important;
        }

        /* Dark mode text improvements */
        body.dark-mode .info-item h3,
        body.dark-mode .info-item p,
        body.dark-mode .card-text,
        body.dark-mode .form-label,
        body.dark-mode .subtitle,
        body.dark-mode .text-muted,
        body.dark-mode p,
        body.dark-mode span,
        body.dark-mode .badge {
            color: var(--text-primary) !important;
        }

        /* Ensure all text is visible in dark mode */
        body.dark-mode .contact-card p,
        body.dark-mode .qr-card p,
        body.dark-mode .info-card p {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Form improvements for light mode - using white text */
        body:not(.dark-mode) .form-control {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.9);
        }

        body:not(.dark-mode) .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary-color);
            color: rgba(255, 255, 255, 1);
        }

        body:not(.dark-mode) .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        /* Form improvements for dark mode */
        body.dark-mode .form-control {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
        }

        body.dark-mode .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary-color);
            color: var(--text-primary);
        }

        body.dark-mode .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: rgba(59, 130, 246, 0.05);
            border-radius: 12px;
            transition: var(--transition);
        }

        .info-item:hover {
            background: rgba(59, 130, 246, 0.1);
            transform: translateX(5px);
        }

        .info-item i {
            font-size: 1.25rem;
            width: 2rem;
            margin-right: 1rem;
            margin-top: 0.25rem;
        }

        .info-item h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }

        .info-item p {
            color: var(--text-light);
            margin: 0;
            line-height: 1.5;
        }

        /* Light mode info-item text improvements - using white text */
        body:not(.dark-mode) .info-item h3 {
            color: rgba(255, 255, 255, 0.9);
        }

        body:not(.dark-mode) .info-item p {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Dark mode info-item text improvements */
        body.dark-mode .info-item h3 {
            color: rgba(255, 255, 255, 0.9);
        }

        body.dark-mode .info-item p {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Card headings (h2) - white in both modes */
        .info-card h2,
        .contact-card h2,
        .qr-card h2,
        .glass-card h2 {
            color: rgba(255, 255, 255, 1) !important;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
        }

        .info-card h2 i,
        .contact-card h2 i,
        .qr-card h2 i,
        .glass-card h2 i {
            color: rgba(255, 255, 255, 0.8) !important;
            margin-right: 0.75rem;
        }

        /* Ensure card headings are white in both light and dark modes */
        body:not(.dark-mode) .info-card h2,
        body:not(.dark-mode) .contact-card h2,
        body:not(.dark-mode) .qr-card h2,
        body:not(.dark-mode) .glass-card h2,
        body.dark-mode .info-card h2,
        body.dark-mode .contact-card h2,
        body.dark-mode .qr-card h2,
        body.dark-mode .glass-card h2 {
            color: rgba(255, 255, 255, 1) !important;
        }

        body:not(.dark-mode) .info-card h2 i,
        body:not(.dark-mode) .contact-card h2 i,
        body:not(.dark-mode) .qr-card h2 i,
        body:not(.dark-mode) .glass-card h2 i,
        body.dark-mode .info-card h2 i,
        body.dark-mode .contact-card h2 i,
        body.dark-mode .qr-card h2 i,
        body.dark-mode .glass-card h2 i {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        /* Button styles */
        .btn_theme {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            border: none;
            border-radius: 50px;
            padding: 0.875rem 2rem;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn_theme:hover {
            color: var(--text-white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: rgba(108, 117, 125, 0.1);
            border: 2px solid rgba(108, 117, 125, 0.3);
            color: var(--text-light);
            backdrop-filter: blur(8px);
        }

        .btn-secondary:hover {
            background: rgba(108, 117, 125, 0.2);
            color: var(--secondary-color);
            border-color: rgba(108, 117, 125, 0.5);
        }

        /* Form styles */
        .form-control {
            border: 2px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            background: rgba(255, 255, 255, 0.9);
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: var(--text-white);
        }

        .form-label {
            font-weight: 500;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }

        /* QR code specific styles */
        .qr-code-container {
            text-align: center;
        }

        .qr-card img {
            border: 3px solid var(--primary-color);
            border-radius: 12px;
            max-width: 100%;
            height: auto;
            background: var(--text-white);
            padding: 0.75rem;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .qr-card img:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        /* Alert styles */
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(8px);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Copy functionality */
        .copy-icon {
            cursor: pointer;
            transition: var(--transition);
        }

        .copy-icon:hover {
            color: var(--primary-color);
            transform: scale(1.1);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .main-container {
                margin: 0;
                border-radius: 0;
                border: none;
                padding: 0;
            }

            body.dark-mode .main-container {
                border: none;
            }

            .header-section {
                padding: 2rem 1rem;
            }

            .header-section h1 {
                font-size: 2rem;
            }

            .content-section {
                padding: 1rem;
            }

            .info-card, .contact-card, .qr-card {
                padding: 1.5rem;
            }

            .info-item {
                flex-direction: row;
                text-align: left;
                align-items: flex-start;
            }

            .info-item i {
                margin-right: 1rem;
                margin-bottom: 0;
                margin-top: 0.25rem;
                flex-shrink: 0;
            }

            .info-item div {
                flex: 1;
            }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus indicators */
        .btn_theme:focus-visible,
        .btn-secondary:focus-visible,
        .form-control:focus-visible {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }
  </style>
</head>

<body style="padding-top: 80px;">
  <!-- Include Public Navigation Body -->
  {% with navbar_section="body" %}
      {% include "public_navbar.html" %}
  {% endwith %}

  <div class="main-container">
    <!-- Back Button -->
    <div class="top-controls">
        <a href="/librarian/library-list/" class="back-button glass-card">
            <i class="fas fa-arrow-left"></i>
            <span> back  </span>
        </a>
    </div>

    <div class="header-section">
        <h1>{{library.library_name}}</h1>
        <p class="subtitle">Premier Library in {{library.librarian_address}}</p>
        {% if library.discount_available %}
        <div class="discount-badge">
            <i class="fas fa-tags"></i>
            Special Discount: {{library.discount_amount}}% Off
        </div>
        {% endif %}
    </div>

    <div class="content-section">
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="info-card glass-card">
                    <h2>
                        <i class="fas fa-info-circle"></i>
                        Library Information
                    </h2>

                    <div class="info-item">
                        <i class="fas fa-map-marker-alt" style="color: #e74c3c;"></i>
                        <div>
                            <h3>Address</h3>
                            <p>{{library.librarian_address}}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-envelope" style="color: #3498db;"></i>
                        <div>
                            <h3>Email</h3>
                            <p>
                                <span id="emailText">{{library.user.email}}</span>
                                <i class="fas fa-copy ms-2 copy-icon" onclick="copyToClipboard('emailText')" title="Copy email"></i>
                                <i class="fas fa-external-link-alt ms-2 copy-icon" onclick="window.location.href = 'mailto:{{library.user.email}}'" title="Send email"></i>
                            </p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-phone" style="color: #3498db;"></i>
                        <div>
                            <h3>Contact</h3>
                            <p>
                                <span id="phoneText">{{library.librarian_phone_num}}</span>
                                <i class="fas fa-copy ms-2 copy-icon" onclick="copyToClipboard('phoneText')" title="Copy phone number"></i>
                                <i class="fas fa-phone-alt ms-2 copy-icon" onclick="window.location.href = 'tel:{{library.librarian_phone_num}}'" title="Call library"></i>
                            </p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-clock" style="color: #f39c12;"></i>
                        <div>
                            <h3>Hours & Pricing</h3>
                            <p>
                                <span class="badge bg-success mb-2">Currently Open</span><br>
                                {% for shift in shifts %}
                                <span class="d-block mt-1">{{shift.name}} - ₹{{shift.price}}</span>
                                {% endfor %}
                            </p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-book-open" style="color: #8e44ad;"></i>
                        <div>
                            <h3>About This Library</h3>
                            <p>{{library.description}}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-map-location-dot" style="color: #27ae60;"></i>
                        <div>
                            <h3>Location</h3>
                            <a href="{{library.google_map_url}}" target="_blank" class="btn btn-sm btn_theme mt-2">
                                <i class="fas fa-map-marker-alt"></i>
                                View on Google Maps
                            </a>
                        </div>
                    </div>

                    {% if library.discount_available %}
                    <div class="info-item" style="background: rgba(245, 158, 11, 0.1); border: 1px solid rgba(245, 158, 11, 0.2);">
                        <i class="fas fa-tags" style="color: #f59e0b;"></i>
                        <div>
                            <h3>Special Offer</h3>
                            <p>Enjoy a {{library.discount_amount}}% discount on all services. Limited time offer for students!</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-card glass-card">
                    {% if success_message %}
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ success_message }}
                    </div>
                    {% endif %}
                    {% if error_message %}
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {{ error_message }}
                    </div>
                    {% endif %}

                    <h2>
                        <i class="fas fa-envelope"></i>
                        Contact {{ library.library_name }}
                    </h2>
                    <p class="text-muted mb-4">Have questions about our services, hours, or facilities? Send us a message and we'll get back to you shortly.</p>

                    <form class="contact-form" method="POST">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-user me-2"></i>Your Name
                            </label>
                            <input type="text" class="form-control" name="name" id="name" placeholder="Enter your full name" required>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                            <input type="email" class="form-control" name="email" id="email" placeholder="Enter your email address" required>
                        </div>

                        <div class="mb-4">
                            <label for="message" class="form-label">
                                <i class="fas fa-message me-2"></i>Your Message
                            </label>
                            <textarea class="form-control" name="message" id="message" rows="4" placeholder="What would you like to know about our library?" required></textarea>
                        </div>

                        <button type="submit" class="btn btn_theme w-100">
                            <i class="fas fa-paper-plane"></i>
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="qr-card glass-card">
                    <h2>
                        <i class="fas fa-qrcode"></i>
                        QR Code for Student Registration
                    </h2>
                    <p class="mb-4">Scan this QR code to register as a student at {{ library.library_name }}</p>

                    <div class="qr-code-container mb-4">
                        <img id="qrImage"
                             src="data:image/png;base64,{{ qr_code }}"
                             alt="QR Code for {{ library.library_name }} student registration"
                             class="img-fluid"
                             loading="lazy">
                    </div>

                    <p class="text-muted mb-4">This QR code provides quick access to our registration system</p>

                    <button id="downloadQR" class="btn btn_theme w-100 mb-3" onclick="downloadQR()">
                        <i class="fas fa-download"></i>
                        Download QR Code
                    </button>

                    <!-- <a href="/librarian/qr-code/{{ library.slug }}/" class="btn btn-outline-primary w-100">
                        <i class="fas fa-expand"></i>
                        View Full QR Page
                    </a> -->
                </div>
            </div>
        </div>


    </div>
  </div>




  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Dark Mode Toggle Functionality
    const darkModeToggle = document.getElementById('darkModeToggle');
    const darkModeIcon = document.getElementById('darkModeIcon');
    const body = document.body;

    // Check for saved dark mode preference or default to light mode
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        body.classList.add('dark-mode');
        darkModeIcon.classList.remove('fa-moon');
        darkModeIcon.classList.add('fa-sun');
    }

    // Dark mode toggle event listener
    darkModeToggle.addEventListener('click', function() {
        body.classList.toggle('dark-mode');

        if (body.classList.contains('dark-mode')) {
            darkModeIcon.classList.remove('fa-moon');
            darkModeIcon.classList.add('fa-sun');
            localStorage.setItem('theme', 'dark');
        } else {
            darkModeIcon.classList.remove('fa-sun');
            darkModeIcon.classList.add('fa-moon');
            localStorage.setItem('theme', 'light');
        }
    });

    // Loader functionality
    $(document).ready(function() {
        $('#loader').show(); // Show the loader on page load

        $(window).on('load', function() {
            $('#loader').hide();
        });

        $('.contact-form').on('submit', function() {
            $('#loader').show();

            // Track form submission event
            gtag('event', 'form_submission', {
              'event_category': 'Contact',
              'event_label': '{{library.library_name}} Contact Form'
            });
        });

        // Track QR code download
        $('#downloadQR').on('click', function() {
            gtag('event', 'qr_download', {
              'event_category': 'Engagement',
              'event_label': '{{library.library_name}} QR Code'
            });
        });
    });
  </script>
  <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast-notification toast-${type}`;
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
            `;

            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                z-index: 9999;
                animation: slideInRight 0.3s ease-out;
                font-size: 0.9rem;
                max-width: 320px;
                backdrop-filter: blur(16px);
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // Enhanced download function
        function downloadQR() {
            try {
                const qrImage = document.getElementById('qrImage');
                if (!qrImage || !qrImage.src) {
                    throw new Error('QR code image not found');
                }

                const link = document.createElement('a');
                link.href = qrImage.src;
                link.download = '{{ library.library_name|slugify }}_qr_code.png';
                link.style.display = 'none';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showToast('QR Code downloaded successfully!', 'success');

                // Track download event
                if (typeof gtag === 'function') {
                    gtag('event', 'qr_download', {
                        'event_category': 'Engagement',
                        'event_label': '{{ library.library_name }} QR Code'
                    });
                }
            } catch (error) {
                console.error('Download failed:', error);
                showToast('Download failed. Please try again.', 'error');
            }
        }

        // Enhanced copy function with toast feedback
        function copyToClipboard(elementId) {
            try {
                const text = document.getElementById(elementId).textContent || document.getElementById(elementId).innerText;
                navigator.clipboard.writeText(text).then(() => {
                    showToast('Copied to clipboard!', 'success');
                }).catch(err => {
                    console.error('Failed to copy text: ', err);
                    showToast('Failed to copy. Please try again.', 'error');
                });
            } catch (error) {
                console.error('Copy failed:', error);
                showToast('Copy failed. Please try again.', 'error');
            }
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling to all links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            // Form validation enhancement
            const contactForm = document.querySelector('.contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    const name = this.querySelector('#name').value.trim();
                    const email = this.querySelector('#email').value.trim();
                    const message = this.querySelector('#message').value.trim();

                    if (!name || !email || !message) {
                        e.preventDefault();
                        showToast('Please fill in all required fields.', 'error');
                        return;
                    }

                    if (!email.includes('@')) {
                        e.preventDefault();
                        showToast('Please enter a valid email address.', 'error');
                        return;
                    }

                    showToast('Sending message...', 'info');
                });
            }

            // Track page view
            if (typeof gtag === 'function') {
                gtag('event', 'library_detail_view', {
                    'event_category': 'Engagement',
                    'event_label': '{{ library.library_name }}'
                });
            }
        });
    </script>
<script>
    window.addEventListener("load", function () {
        const path = window.location.pathname; // Get current page path
        let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

        // Increment count for the current path
        pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

        // Store updated data back to localStorage
        localStorage.setItem("page_data", JSON.stringify(pageData));
      });

      // Function to send page view data
      function sendPageData() {
        const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

        if (Object.keys(pageData).length > 0) {
          fetch(location.origin + "/librarian/track-page-view/", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-CSRFToken": "{{ csrf_token }}",
            },
            body: JSON.stringify(pageData),
          })

            .then(() => {
              localStorage.removeItem("page_data");
            })
            .catch((error) => console.error("Error sending page data:", error));
            localStorage.removeItem("page_data");
        } else {

          console.log("No page data to send");
        }
      }

      // Send data every 10 seconds
      setInterval(sendPageData, 10000);
</script>
</body>

</html>
