<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ invoice.student.name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa, #e0e4ff, #fceefc, #eefafc);
            background-size: 400% 400%;
            animation: pearlGradient 15s ease infinite;
            position: relative;
            overflow-x: hidden;
        }

        @keyframes pearlGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .invoice-container {
            max-width: 1265px;
            margin: auto;
            background: #fff;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1;
            background-position: top right;
            background-repeat: no-repeat;
        }

        .header, .section, .footer { margin-bottom: 1.5rem; }
        .header h2 { margin-bottom: 0.5rem; }
        .header .library-name {
            font-size: 2.5rem;
            font-weight: 900;
            color: #4f46e5;
            margin-top: 0.25rem;
        }
        .header .library-logo {
            width: 100%;
            aspect-ratio: 5 / 1;
            object-fit: contain;
            margin-bottom: 0.5rem;
        }
        .section h5 { margin-bottom: 1rem; border-bottom: 1px solid #dee2e6; padding-bottom: 0.5rem; }
        .info { margin-bottom: 0.5rem; }
        .info span { display: inline-block; min-width: 120px; font-weight: 600; }
        table { width: 100%; margin-top: 1rem; }
        th, td { padding: 0.75rem; text-align: left; border-bottom: 1px solid #dee2e6; }
        .text-end { text-align: end; }
        .btn { margin-right: 0.5rem; }

        .green-thank-you {
            background-color: #e6f4ea;
            color: #1b5e20;
            padding: 1.25rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            text-align: center;
            font-size: 0.95rem;
            font-weight: 500;
            position: relative;
        }
        .green-thank-you::before {
            content: "\1F331";
            font-size: 1.5rem;
            position: absolute;
            top: 0.75rem;
            left: 1rem;
        }

        .student-photo {
            float: right;
            width: 150px;
            height: 200px;
            object-fit: cover;
            background-color: #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            font-weight: bold;
            border: 1px solid black;
            box-shadow: 0 0 0 4px #fff, 0 0 0 5px black;
        }

        .gmap-callout {
            margin-top: 1rem;
            background-color: #e8f5e9;
            color: #1b5e20;
            padding: 1rem;
            border-left: 5px solid #43a047;
            border-radius: 0.5rem;
            font-weight: 500;
        }
        .gmap-callout a {
            color: #1b5e20;
            text-decoration: underline;
            font-weight: bold;
        }

        /* Payment Status Styles */
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-unpaid {
            background-color: #fee2e2;
            color: #dc2626;
        }

        .status-partiallypaid {
            background-color: #fef3c7;
            color: #d97706;
        }

        .status-paid {
            background-color: #dcfce7;
            color: #16a34a;
        }

        .remaining-row {
            background-color: #fef3c7;
            font-weight: bold;
        }

        /* Payment Form Styles */
        .payment-form-section {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .payment-form .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .payment-form .form-group {
            display: flex;
            flex-direction: column;
        }

        .payment-form label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #374151;
        }

        .payment-form .form-control {
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 1rem;
        }

        .payment-form .form-control:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .payment-form .btn {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-form .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        @media (max-width: 768px) {
            .payment-form .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* Payment Breakdown Styles */
        .subtotal-row {
            background-color: #f8f9fa;
            font-weight: 500;
        }

        .discount-row {
            background-color: #fff3cd;
            color: #856404;
        }

        .net-amount-row {
            background-color: #d1ecf1;
            color: #0c5460;
            font-weight: 600;
        }

        .paid-amount-row {
            background-color: #d4edda;
            color: #155724;
            font-weight: 600;
        }

        .invoice-note {
            margin-top: 1rem;
            padding: 0.75rem;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-left: 4px solid #2196f3;
            border-radius: 0 8px 8px 0;
        }

        .invoice-note p {
            margin: 0;
            font-weight: 500;
            color: #1565c0;
        }

        /* Library Contact Styles */
        .library-contact {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .contact-item i {
            color: #6c757d;
            font-size: 1.25rem;
            margin-top: 0.25rem;
            min-width: 20px;
        }

        .contact-item strong {
            display: block;
            color: #495057;
            margin-bottom: 0.25rem;
        }

        .contact-item p {
            margin: 0;
            color: #6c757d;
        }

        .contact-item a {
            color: #007bff;
            text-decoration: none;
        }

        .contact-item a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
        <script type="application/ld+json">
        {
          "@context": "http://schema.org",
          "@type": "Invoice",
          "paymentDue": "{{ invoice.due_date|date:'Y-m-d' }}",
          "paymentStatus": "http://schema.org/PaymentComplete",
          "paymentMethod": "{{ invoice.mode_pay }}",
          "totalPaymentDue": {
            "@type": "PriceSpecification",
            "price": "{{ invoice.total_amount }}",
            "priceCurrency": "INR"
          },
          "provider": {
            "@type": "Organization",
            "name": "{{ invoice.student.librarian.library_name }}",
            "email": "{{ invoice.student.librarian.email }}",
            "url": "{{ invoice.student.librarian.website }}"
          },
          "customer": {
            "@type": "Person",
            "name": "{{ invoice.student.name }}"
          }
        }
    </script>
</head>
<body>
<div class="invoice-container">
    <div class="header text-center">
        {% if invoice.student.librarian.image %}
        <img src="{{ invoice.student.librarian.image.url }}" alt="Library Logo" class="library-logo">
        {% endif %}
        <h2>Payment Receipt</h2>
        <div class="library-name">{{ invoice.student.librarian.library_name }}</div>
    </div>

    <div class="green-thank-you">
        🌱 Thank you for choosing e-invoicing and contributing to environmental protection. Your action helps reduce paper usage and save trees!
    </div>

    <div class="section">
        <h5>Student Details</h5>
        {% if invoice.student.image %}
        <img src="{{ invoice.student.image.url }}" alt="Student Photo" class="student-photo">
        {% else %}
        <div class="student-photo">{{ invoice.student.name|first|upper }}</div>
        {% endif %}
        <div class="info"><span>Name:</span> {{ invoice.student.name }}</div>
        <div class="info"><span>Course:</span> {{ invoice.student.course }}</div>
        <div class="info"><span>Email:</span> {{ invoice.student.email }}</div>
        <div class="info"><span>Mobile:</span> {{ invoice.student.mobile }}</div>
    </div>

    <div class="section">
        <h5>Library Details</h5>
        <div class="info"><span>Library:</span> {{ invoice.student.librarian.library_name }}</div>
        <div class="info"><span>Address:</span> {{ invoice.student.librarian.librarian_address }}</div>
        <div class="info"><span>Contact:</span> <a href="tel:{{ invoice.student.librarian.librarian_phone_num }}">{{ invoice.student.librarian.librarian_phone_num }}</a></div>
        <div class="info"><span>Email:</span> <a href="mailto:{{ invoice.student.librarian.user.email }}">{{ invoice.student.librarian.user.email }}</a></div>
        <div class="info"><span>Website:</span> {{ invoice.student.librarian.website }}</div>
        <div class="info">
            <span>Location:</span>
            <a href="{{ invoice.student.librarian.google_map_url }}" target="_blank">View on Google Maps</a>
        </div>

        <div class="gmap-callout">
            🌍 Do you like the service? What did you like the most? <a href="https://www.google.com/maps/search/?api=1&query={{ invoice.student.librarian.librarian_address|urlencode }}" target="_blank">Tell us on Google Maps</a> — this helps us reach more students!
        </div>
    </div>

    <div class="section">
        <h5>Invoice Info</h5>
        <div class="info"><span>Invoice No.:</span> #{{ invoice.invoice_id|default:invoice.id }}</div>
        <div class="info"><span>Issue Date:</span> {{ invoice.issue_date }}</div>
        <div class="info"><span>Due Date:</span> {{ invoice.due_date }}</div>
        <div class="info"><span>Payment Status:</span>
            <span class="status-badge status-{{ invoice.payment_status|lower|cut:' ' }}">
                {{ invoice.payment_status }}
            </span>
        </div>
        {% if invoice.payment_status != 'Paid' %}
        <div class="info"><span>Remaining Balance:</span> <strong>₹{{ invoice.remaining_due }}</strong></div>
        {% endif %}
    </div>

    <div class="section">
        <h5>Payment Breakdown</h5>
        <table>
            <thead>
                <tr><th>Description</th><th>Period</th><th class="text-end">Amount</th></tr>
            </thead>
            <tbody>
                {% for shift in invoice.shift.all %}
                <tr>
                    <td>{{ shift.name }}  | {{ shift.time_range }} </td>
                    <td>
                        {% for month in invoice.months.all %}{{ month.name }}{% if not forloop.last %}, {% endif %}{% endfor %}
                    </td>
                    <td class="text-end">₹{{ shift.price }}</td>
                </tr>
                {% endfor %}

                <!-- Calculation Summary -->
                <tr class="subtotal-row">
                    <td colspan="2"><strong>Gross Amount</strong></td>
                    <td class="text-end"><strong>₹{% if invoice.discount_amount %}{{ invoice.total_amount|add:invoice.discount_amount }}{% else %}{{ invoice.total_amount }}{% endif %}</strong></td>
                </tr>
                {% if invoice.discount_amount %}
                <tr class="discount-row">
                    <td colspan="2"><strong>Discount</strong></td>
                    <td class="text-end"><strong>-₹{{ invoice.discount_amount }}</strong></td>
                </tr>
                {% endif %}
                <tr class="net-amount-row">
                    <td colspan="2"><strong>Net Amount</strong></td>
                    <td class="text-end"><strong>₹{{ invoice.total_amount }}</strong></td>
                </tr>
                <tr class="paid-amount-row">
                    <td colspan="2"><strong>Amount Paid</strong></td>
                    <td class="text-end"><strong>₹{{ invoice.total_paid }}</strong></td>
                </tr>
                {% if invoice.remaining_due > 0 %}
                <tr class="remaining-row">
                    <td colspan="2"><strong>Amount Remaining</strong></td>
                    <td class="text-end"><strong>₹{{ invoice.remaining_due }}</strong></td>
                </tr>
                {% endif %}
            </tbody>
        </table>

        <div class="invoice-note">
            <p><strong>Note:</strong> Invoice created for Net Amount of ₹{{ invoice.total_amount }}</p>
        </div>
    </div>

    <!-- Payment History Section -->
    <div class="section">
        <h5>Payment History</h5>
        {% if invoice.payment_set.all %}
        <table>
            <thead>
                <tr>
                    <th>S.No.</th>
                    <th>Date</th>
                    <th>Amount</th>
                    <th>Mode</th>
                    <th>Notes</th>
                </tr>
            </thead>
            <tbody>
                {% for payment in invoice.payment_set.all %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ payment.payment_date|date:"d M Y" }}</td>
                    <td>₹{{ payment.amount_paid }}</td>
                    <td>{{ payment.payment_mode }}</td>
                    <td>{{ payment.notes|default:"-" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p class="text-muted">No payments recorded yet.</p>
        {% endif %}
    </div>

    <!-- Record Payment Section (for staff only) -->
    {% if role == 'Librarian' or role == 'Sublibrarian' %}
    {% if invoice.remaining_due > 0 %}
    <div class="section payment-form-section">
        <h5>Record Additional Payment</h5>
        <form method="POST" action="{% url 'record_payment' invoice.slug %}" class="payment-form">
            {% csrf_token %}
            <div class="form-row">
                <div class="form-group">
                    <label for="amount_paid">Amount Paid</label>
                    <input type="number" name="amount_paid" id="amount_paid" class="form-control"
                           min="1" max="{{ invoice.remaining_due }}" required>
                    <small class="form-text text-muted">Maximum: ₹{{ invoice.remaining_due }}</small>
                </div>
                <div class="form-group">
                    <label for="payment_mode">Payment Mode</label>
                    <select name="payment_mode" id="payment_mode" class="form-control" required>
                        <option value="Cash">Cash</option>
                        <option value="Online">Online</option>
                        <option value="UPI">UPI</option>
                        <option value="Card">Card</option>
                        <option value="Bank Transfer">Bank Transfer</option>
                        <option value="Cheque">Cheque</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="next_payment_date">Next Payment Date (Optional)</label>
                    <input type="date" name="next_payment_date" id="next_payment_date" class="form-control">
                </div>
                <div class="form-group">
                    <label for="notes">Notes (Optional)</label>
                    <input type="text" name="notes" id="notes" class="form-control" placeholder="Payment notes">
                </div>
            </div>
            <button type="submit" class="btn btn-primary">Record Payment</button>
        </form>
    </div>
    {% endif %}
    {% endif %}


    <div class="footer text-center">
        <p>Thank you for your payment!</p>
        <p>This is a computer-generated receipt. For queries: <a href="mailto:{{ invoice.student.librarian.user.email }}">{{ invoice.student.librarian.user.email }}</a></p>
        <p style="font-size: 0.8rem;">Generated on {{ invoice.issue_date|date:"d M Y" }}</p>
    </div>

    <div class="text-center no-print">
        <button class="btn btn-success" onclick="downloadInvoice()">Download</button>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
    function downloadInvoice() {
        const invoiceContainer = document.querySelector('.invoice-container');
        html2canvas(invoiceContainer, {
            scale: 2,
            useCORS: true
        }).then(canvas => {
            const link = document.createElement('a');
            link.download = `invoice_{{ invoice.invoice_id|default:invoice.id }}.png`;
            link.href = canvas.toDataURL();
            link.click();
        });
    }
</script>
</body>
</html>
