<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Google Tag Manager -->
    <script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-J6148FSF9M&amp;cx=c&amp;gtm=45He5781v9221326947za200&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********"></script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Basic Meta Tags -->
    <title>Verify OTP - Librainian</title>
    <meta name="description" content="Verify your OTP to complete your Librainian account registration.">
    <meta name="robots" content="noindex, nofollow">
    <meta name="theme-color" content="#6366f1" id="theme-color-meta">
    <meta name="apple-mobile-web-app-status-bar-style" content="default" id="status-bar-meta">

    <!-- Modern Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

<style>
        /* CSS Variables */
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-white: #ffffff;
            --border-radius: 16px;
            --transition: all 0.3s ease;
            --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* Global Styles */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Comfortaa', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        /* Dark Mode Support */
        body.dark-mode {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        }

        /* Main Container */
        .main-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem 1rem;
            width: 100%;
        }

        .otp-container {
            width: 100%;
            max-width: 450px;
        }

        .otp-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .otp-icon {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .otp-icon i {
            font-size: 3.5rem;
            color: #667eea;
        }

        .otp-title {
            text-align: center;
            font-size: 1.75rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .otp-description {
            text-align: center;
            color: #718096;
            margin-bottom: 2rem;
            font-size: 0.95rem;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 1rem 1.25rem;
            font-size: 1.1rem;
            text-align: center;
            letter-spacing: 0.5rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .verify-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            margin-top: 1.5rem;
            transition: all 0.3s ease;
        }

        .verify-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .resend-btn {
            background: transparent;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            color: #718096;
            width: 100%;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }

        .resend-btn:hover:not(:disabled) {
            border-color: #667eea;
            color: #667eea;
        }

        .resend-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .timer-display {
            text-align: center;
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #718096;
        }

        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }

        .footer-links {
            text-align: center;
            margin-top: 2rem;
        }

        .footer-links a {
            color: #718096;
            text-decoration: none;
            margin: 0 1rem;
            font-size: 0.9rem;
        }

        .footer-links a:hover {
            color: #667eea;
        }

        /* Dark mode toggle styles */
        .dark-mode-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 50px;
            padding: 0.5rem 1rem;
            color: var(--text-white);
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: var(--transition);
            z-index: 1000;
        }

        .dark-mode-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem 0.5rem;
            }

            .otp-card {
                padding: 2rem 1.5rem;
            }

            .otp-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <!-- Dark Mode Toggle -->
    <div class="dark-mode-toggle" onclick="toggleDarkMode()">
        <i class="fas fa-moon" id="dark-mode-icon"></i>
    </div>

    <div class="main-container">
        <div class="otp-container">
        <div class="otp-card">
            <!-- OTP Icon -->
            <div class="otp-icon">
                <i class="fas fa-key"></i>
            </div>

            <!-- Title and Description -->
            <h1 class="otp-title">Verify Your Code</h1>
            <p class="otp-description">Enter the 6-digit verification code sent to your email</p>

            <!-- Alert Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags|default:'primary' }}" role="alert">
                        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <!-- OTP Form -->
            <form method="POST" id="otpForm">
                {% csrf_token %}
                <div class="mb-3">
                    <input type="text"
                           class="form-control"
                           id="otp"
                           name="otp"
                           placeholder="Enter 6-digit OTP"
                           required
                           maxlength="6"
                           pattern="[0-9]{6}"
                           autocomplete="one-time-code"
                           inputmode="numeric">
                </div>

                <button type="submit" class="verify-btn">
                    <i class="fas fa-check-circle me-2"></i>
                    Verify Code
                </button>
            </form>

            <!-- Timer and Resend -->
            <div class="timer-display" id="timerDisplay">
                <i class="fas fa-clock me-1"></i>
                You can request a new code in <span id="countdown">60</span> seconds
            </div>

            <button type="button" class="resend-btn" id="resendBtn" disabled>
                <i class="fas fa-redo me-2"></i>
                Resend Verification Code
            </button>

            <!-- Footer Links -->
            <div class="footer-links">
                <a href="/librarian/login/">
                    <i class="fas fa-arrow-left me-1"></i>Back to Login
                </a>
                <a href="/">
                    <i class="fas fa-home me-1"></i>Home
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('otpForm');
        const otpInput = document.getElementById('otp');
        const resendBtn = document.getElementById('resendBtn');
        const timerDisplay = document.getElementById('timerDisplay');
        const countdown = document.getElementById('countdown');

        let timeLeft = 60;
        let timerInterval;

        // Start countdown timer
        function startTimer() {
            timerInterval = setInterval(() => {
                timeLeft--;
                countdown.textContent = timeLeft;

                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    timerDisplay.innerHTML = '<i class="fas fa-clock me-1"></i>You can now request a new verification code';
                    resendBtn.disabled = false;
                }
            }, 1000);
        }

        // OTP input formatting
        otpInput.addEventListener('input', function() {
            // Only allow numbers
            this.value = this.value.replace(/\D/g, '');

            // Limit to 6 digits
            if (this.value.length > 6) {
                this.value = this.value.slice(0, 6);
            }
        });



        // Resend OTP functionality
        resendBtn.addEventListener('click', function() {
            if (!this.disabled) {
                window.location.href = '/librarian/resend-otp/';
            }
        });

        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            }, 500);
        });

        // Initialize
        startTimer();
        otpInput.focus();
    });

    // Dark mode functionality
    function toggleDarkMode() {
        const body = document.body;
        const icon = document.getElementById('dark-mode-icon');

        body.classList.toggle('dark-mode');

        if (body.classList.contains('dark-mode')) {
            icon.className = 'fas fa-sun';
            localStorage.setItem('darkMode', 'enabled');
        } else {
            icon.className = 'fas fa-moon';
            localStorage.setItem('darkMode', 'disabled');
        }
    }

    // Initialize dark mode from localStorage
    document.addEventListener('DOMContentLoaded', function() {
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.body.classList.add('dark-mode');
            document.getElementById('dark-mode-icon').className = 'fas fa-sun';
        }
    });
</script>
</body>
</html>
