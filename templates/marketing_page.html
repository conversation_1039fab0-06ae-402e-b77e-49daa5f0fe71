{% extends "base.html" %}

{% block title %}Marketing Dashboard - Librainian{% endblock %}



{% block content %}
<div class="marketing-content fade-in">
    <!-- Alert Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert-glass alert-{{ message.tags|default:'info' }} alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close-glass" data-bs-dismiss="alert" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Marketing Management Section -->
    <div class="row g-4">
        <!-- Student Data Management -->
        <div class="col-12">
            <div class="modern-card">
                <div class="modern-card-body">
                    <!-- Desktop Filters -->
                    <div class="filters-section-desktop d-none d-md-block mb-4">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <select id="courseDropdown" class="form-control-glass" onchange="applyFilters()">
                                    <option value="">All Courses</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select id="nameDropdown" class="form-control-glass" onchange="applyFilters()">
                                    <option value="">All Names</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select id="genderDropdown" class="form-control-glass" onchange="applyFilters()">
                                    <option value="">All Genders</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select id="phoneDropdown" class="form-control-glass" onchange="applyFilters()">
                                    <option value="">All Phones</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" id="startDate" class="form-control-glass" onchange="applyFilters()" placeholder="Start Date" />
                            </div>
                            <div class="col-md-2">
                                <input type="date" id="endDate" class="form-control-glass" onchange="applyFilters()" placeholder="End Date" />
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Filter Button -->
                    <div class="d-block d-md-none mb-3">
                        <button class="btn-filter-mobile" type="button" data-bs-toggle="collapse" data-bs-target="#mobileFilters" aria-expanded="false">
                            <i class="fas fa-filter me-2"></i>Filters
                        </button>
                        <div class="collapse mt-3" id="mobileFilters">
                            <div class="mobile-filters-card">
                                <div class="row g-2">
                                    <div class="col-12">
                                        <select id="mobileCourseDrop" class="form-control-glass" onchange="applyMobileFilters()">
                                            <option value="">All Courses</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <select id="mobileGenderDrop" class="form-control-glass" onchange="applyMobileFilters()">
                                            <option value="">All Genders</option>
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <input type="date" id="mobileStartDate" class="form-control-glass" onchange="applyMobileFilters()" />
                                    </div>
                                    <div class="col-6">
                                        <input type="date" id="mobileEndDate" class="form-control-glass" onchange="applyMobileFilters()" />
                                    </div>
                                    <div class="col-12">
                                        <button class="btn-clear-filters" onclick="clearMobileFilters()">Clear Filters</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Desktop Table View -->
                    <div class="d-none d-md-block">
                        <form method="POST" action="/librarian/process-student-data/">
                            {% csrf_token %}
                            <div class="table-responsive">
                                <table class="table-glass table-hover">
                                    <thead>
                                        <tr>
                                            <th>
                                                <div class="select-all-header">
                                                    <input type="checkbox" onclick="checkAll()" class="checkbox-glass" />
                                                    <span>Select</span>
                                                </div>
                                            </th>
                                            <th>
                                                Course <i class="fas fa-sort sort-icon" onclick="sortTable(1)"></i>
                                            </th>
                                            <th>
                                                Name <i class="fas fa-sort sort-icon" onclick="sortTable(2)"></i>
                                            </th>
                                            <th>
                                                Gender <i class="fas fa-sort sort-icon" onclick="sortTable(3)"></i>
                                            </th>
                                            <th>
                                                Phone <i class="fas fa-sort sort-icon" onclick="sortTable(4)"></i>
                                            </th>
                                            <th>
                                                Email <i class="fas fa-sort sort-icon" onclick="sortTable(5)"></i>
                                            </th>
                                            <th>
                                                Due Date <i class="fas fa-sort sort-icon" onclick="sortTable(6, true)"></i>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="studentTableBody">
                                        {% for data in student_data %}
                                        <tr class="student-row-marketing"
                                            data-course-name="{{ data.student.course }}"
                                            data-name="{{ data.student.name }}"
                                            data-gender="{{ data.student.gender }}"
                                            data-phone="{{ data.student.mobile }}"
                                            data-email="{{ data.student.email }}"
                                            data-due-date="{{ data.invoice_data.due_date }}">
                                            <td>
                                                <input type="checkbox" name="student_checkbox" value="{{ data.student.slug }}"
                                                       onclick="checkThis(event)" class="checkbox-glass" />
                                            </td>
                                            <td>
                                                <span class="course-badge-marketing">{{ data.student.course }}</span>
                                            </td>
                                            <td class="student-name-marketing">
                                                <div class="student-info-marketing">
                                                    <div class="student-avatar-marketing">
                                                        {{ data.student.name|first|upper }}
                                                    </div>
                                                    <span>{{ data.student.name }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="gender-badge-marketing gender-{{ data.student.gender|lower }}">
                                                    <i class="fas fa-{% if data.student.gender == 'Male' %}mars{% elif data.student.gender == 'Female' %}venus{% else %}genderless{% endif %} me-1"></i>
                                                    {{ data.student.gender }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="contact-info-marketing">
                                                    <span>{{ data.student.mobile }}</span>
                                                    <a href="tel:{{ data.student.mobile }}" class="contact-link-marketing">
                                                        <i class="fas fa-phone"></i>
                                                    </a>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="email-info-marketing">
                                                    <span class="email-text">{{ data.student.email }}</span>
                                                    <a href="mailto:{{ data.student.email }}" class="contact-link-marketing">
                                                        <i class="fas fa-envelope"></i>
                                                    </a>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="due-date-marketing">
                                                    <i class="fas fa-calendar-alt me-1"></i>
                                                    {{ data.invoice_data.due_date }}
                                                </div>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="empty-state-marketing">
                                                    <i class="fas fa-bullhorn fa-3x mb-3"></i>
                                                    <h5>No Marketing Data Available</h5>
                                                    <p>There are no students available for marketing campaigns.</p>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="marketing-actions-desktop">
                                <button type="submit" id="proceedButton" class="btn-proceed-marketing disabled">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Proceed with Marketing
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Mobile Card View -->
                    <div class="d-block d-md-none">
                        <form method="POST" action="/librarian/process-student-data/">
                            {% csrf_token %}
                            {% for data in student_data %}
                            <div class="student-card-mobile-marketing mb-3 slide-up"
                                 style="animation-delay: {{ forloop.counter0|add:1|floatformat:1 }}s">
                                <div class="card-body-mobile-marketing">
                                    <div class="position-relative">
                                        <!-- Checkbox in top right corner -->
                                        <input type="checkbox" name="student_checkbox" value="{{ data.student.slug }}"
                                               onclick="checkThis(event)" class="checkbox-glass-mobile-top-right" />

                                        <div class="student-info-mobile-marketing">
                                                <div class="student-header-mobile">
                                                    <div class="student-avatar-mobile-marketing">
                                                        {{ data.student.name|first|upper }}
                                                    </div>
                                                    <div class="student-details-mobile">
                                                        <h6 class="student-name-mobile-marketing">{{ data.student.name }}</h6>
                                                        <span class="course-badge-mobile-marketing">{{ data.student.course }}</span>
                                                    </div>
                                                </div>
                                                <div class="student-contact-mobile">
                                                    <div class="contact-item-mobile">
                                                        <i class="fas fa-phone me-2"></i>
                                                        <span>{{ data.student.mobile }}</span>
                                                        <a href="tel:{{ data.student.mobile }}" class="contact-link-mobile">
                                                            <i class="fas fa-phone-alt"></i>
                                                        </a>
                                                    </div>
                                                    <div class="contact-item-mobile">
                                                        <i class="fas fa-envelope me-2"></i>
                                                        <span class="email-mobile">{{ data.student.email }}</span>
                                                        <a href="mailto:{{ data.student.email }}" class="contact-link-mobile">
                                                            <i class="fas fa-envelope"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="student-meta-mobile">
                                                    <span class="gender-badge-mobile gender-{{ data.student.gender|lower }}">
                                                        <i class="fas fa-{% if data.student.gender == 'Male' %}mars{% elif data.student.gender == 'Female' %}venus{% else %}genderless{% endif %} me-1"></i>
                                                        {{ data.student.gender }}
                                                    </span>
                                                    <span class="due-date-mobile">
                                                        <i class="fas fa-calendar-alt me-1"></i>
                                                        {{ data.invoice_data.due_date }}
                                                    </span>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="empty-state-mobile-marketing">
                                <i class="fas fa-bullhorn fa-3x mb-3"></i>
                                <h5>No Marketing Data Available</h5>
                                <p>There are no students available for marketing campaigns.</p>
                            </div>
                            {% endfor %}
                            <div class="marketing-actions-mobile">
                                <button type="submit" id="proceedButtonMobile" class="btn-proceed-marketing-mobile disabled">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Proceed with Marketing
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-lg-3 col-md-6">
            <div class="stats-card slide-up">
                <div class="stats-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <h3 class="stats-value">{{ student_data|length|default:0 }}</h3>
                <p class="stats-label">Marketing Targets</p>
                <div class="stats-trend">
                    <small class="text-warning">
                        <i class="fas fa-target me-1"></i>
                        Available for campaigns
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card slide-up" style="animation-delay: 0.1s;">
                <div class="stats-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="stats-value">{{ campaigns_sent|default:0 }}</h3>
                <p class="stats-label">Campaigns Sent</p>
                <div class="stats-trend">
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>
                        This month
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card slide-up" style="animation-delay: 0.2s;">
                <div class="stats-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                    <i class="fas fa-eye"></i>
                </div>
                <h3 class="stats-value">{{ open_rate|default:0 }}%</h3>
                <p class="stats-label">Open Rate</p>
                <div class="stats-trend">
                    <small class="text-info">
                        <i class="fas fa-chart-line me-1"></i>
                        Campaign performance
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card slide-up" style="animation-delay: 0.3s;">
                <div class="stats-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="fas fa-mouse-pointer"></i>
                </div>
                <h3 class="stats-value">{{ click_rate|default:0 }}%</h3>
                <p class="stats-label">Click Rate</p>
                <div class="stats-trend">
                    <small class="text-success">
                        <i class="fas fa-hand-pointer me-1"></i>
                        Engagement rate
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Glassmorphism Marketing Theme */
    .marketing-content {
        min-height: calc(100vh - 160px);
        padding: 0;
        margin: 0;
        position: relative;
    }

    .marketing-content::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    .modern-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .modern-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .modern-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        opacity: 0.8;
    }

    .modern-card-header {
        padding: 1.5rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
    }

    .modern-card-body {
        padding: 2rem;
        position: relative;
        z-index: 2;
    }

    .modern-card-header h5 {
        color: white;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        font-size: 1.1rem;
    }

    .card-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .marketing-stats-badge {
        background: rgba(245, 158, 11, 0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(245, 158, 11, 0.5);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.875rem;
    }

    /* Form Controls */
    .form-control-glass {
        background: rgba(255, 255, 255, 0.15);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        color: white;
        padding: 0.75rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .form-control-glass:focus {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    }

    .form-control-glass::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .form-control-glass option {
        background: rgba(102, 126, 234, 0.9);
        color: white;
    }

    /* Mobile Filter Button */
    .btn-filter-mobile {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        width: 100%;
    }

    .btn-filter-mobile:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: translateY(-1px);
    }

    .mobile-filters-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }

    .btn-clear-filters {
        background: rgba(239, 68, 68, 0.3);
        border: 2px solid rgba(239, 68, 68, 0.5);
        color: white;
        padding: 0.75rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        width: 100%;
    }

    .btn-clear-filters:hover {
        background: rgba(239, 68, 68, 0.5);
        border-color: rgba(239, 68, 68, 0.7);
        color: white;
        transform: translateY(-1px);
    }

    /* Table Styling */
    .table-glass {
        width: 100%;
        margin: 0;
        background: transparent;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table-glass th {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        font-weight: 700;
        border: none;
        padding: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        font-size: 0.95rem;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .table-glass td {
        color: white;
        border: none;
        padding: 1rem;
        vertical-align: middle;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .student-row-marketing {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .student-row-marketing:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.01);
    }

    .checkbox-glass {
        width: 18px;
        height: 18px;
        accent-color: #3b82f6;
        cursor: pointer;
        box-shadow: none !important;
        outline: none !important;
    }

    .checkbox-glass:hover {
        box-shadow: none !important;
        outline: none !important;
    }

    .checkbox-glass:focus {
        box-shadow: none !important;
        outline: none !important;
    }

    .select-all-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
    }

    .sort-icon {
        cursor: pointer;
        opacity: 0.7;
        transition: all 0.3s ease;
        margin-left: 0.5rem;
    }

    .sort-icon:hover {
        opacity: 1;
        transform: scale(1.1);
    }

    /* Student Info */
    .student-info-marketing {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .student-avatar-marketing {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.4);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* Badges */
    .course-badge-marketing {
        background: rgba(16, 185, 129, 0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(16, 185, 129, 0.5);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.85rem;
    }

    .gender-badge-marketing {
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.85rem;
    }

    .gender-male {
        background: rgba(59, 130, 246, 0.3);
        border-color: rgba(59, 130, 246, 0.5);
        color: #3b82f6;
    }

    .gender-female {
        background: rgba(236, 72, 153, 0.3);
        border-color: rgba(236, 72, 153, 0.5);
        color: #ec4899;
    }

    .gender-other {
        background: rgba(139, 92, 246, 0.3);
        border-color: rgba(139, 92, 246, 0.5);
        color: #8b5cf6;
    }

    /* Contact Info */
    .contact-info-marketing,
    .email-info-marketing {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .contact-link-marketing {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        padding: 0.25rem;
        border-radius: 6px;
    }

    .contact-link-marketing:hover {
        color: white;
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }

    .email-text {
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .due-date-marketing {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.875rem;
    }

    /* Action Buttons */
    .btn-proceed-marketing {
        background: rgba(16, 185, 129, 0.3);
        border: 2px solid rgba(16, 185, 129, 0.5);
        color: white;
        padding: 1rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .btn-proceed-marketing:hover:not(.disabled) {
        background: rgba(16, 185, 129, 0.5);
        border-color: rgba(16, 185, 129, 0.7);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    .btn-proceed-marketing.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: rgba(107, 114, 128, 0.3);
        border-color: rgba(107, 114, 128, 0.5);
    }

    .marketing-actions-desktop {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Mobile Cards */
    .student-card-mobile-marketing {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .student-card-mobile-marketing:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .card-body-mobile-marketing {
        padding: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .checkbox-glass-mobile {
        width: 20px;
        height: 20px;
        accent-color: #3b82f6;
        cursor: pointer;
        box-shadow: none !important;
        outline: none !important;
    }

    .checkbox-glass-mobile:hover,
    .checkbox-glass-mobile:focus {
        box-shadow: none !important;
        outline: none !important;
    }

    .checkbox-glass-mobile-top-right {
        position: absolute;
        top: 0rem;
        right: 0rem;
        width: 20px;
        height: 20px;
        accent-color: #3b82f6;
        cursor: pointer;
        z-index: 10;
        box-shadow: none !important;
        outline: none !important;
    }

    .checkbox-glass-mobile-top-right:hover,
    .checkbox-glass-mobile-top-right:focus {
        box-shadow: none !important;
        outline: none !important;
    }

    .student-header-mobile {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .student-avatar-mobile-marketing {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 800;
        font-size: 1.25rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.4);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .student-name-mobile-marketing {
        color: white;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        font-size: 1.1rem;
    }

    .course-badge-mobile-marketing {
        background: rgba(16, 185, 129, 0.3);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 8px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(16, 185, 129, 0.5);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.75rem;
    }

    .student-contact-mobile {
        margin-bottom: 1rem;
    }

    .contact-item-mobile {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.9);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .contact-item-mobile i {
        width: 20px;
        color: rgba(255, 255, 255, 0.7);
    }

    .email-mobile {
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .contact-link-mobile {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        margin-left: auto;
        transition: all 0.3s ease;
        padding: 0.25rem;
        border-radius: 6px;
    }

    .contact-link-mobile:hover {
        color: white;
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }

    .student-meta-mobile {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .gender-badge-mobile {
        padding: 0.25rem 0.75rem;
        border-radius: 8px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.75rem;
    }

    .due-date-mobile {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.75rem;
    }

    .marketing-actions-mobile {
        text-align: center;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .btn-proceed-marketing-mobile {
        background: rgba(16, 185, 129, 0.3);
        border: 2px solid rgba(16, 185, 129, 0.5);
        color: white;
        padding: 1rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        width: 100%;
    }

    .btn-proceed-marketing-mobile:hover:not(.disabled) {
        background: rgba(16, 185, 129, 0.5);
        border-color: rgba(16, 185, 129, 0.7);
        color: white;
        transform: translateY(-2px);
    }

    .btn-proceed-marketing-mobile.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: rgba(107, 114, 128, 0.3);
        border-color: rgba(107, 114, 128, 0.5);
    }

    /* Stats Cards, Alerts, and Empty States */
    .stats-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .stats-value {
        font-size: 2rem;
        font-weight: 800;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        margin: 0;
    }

    .stats-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .empty-state-marketing,
    .empty-state-mobile-marketing {
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
        padding: 3rem 1rem;
    }

    .empty-state-marketing i,
    .empty-state-mobile-marketing i {
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 1rem;
    }

    .empty-state-marketing h5,
    .empty-state-mobile-marketing h5 {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .alert-glass {
        background: rgba(255, 255, 255, 0.15);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        color: white;
        padding: 1rem 1.5rem;
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }

    .btn-close-glass {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        color: white;
        padding: 0.5rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        cursor: pointer;
        width: 32px;
        height: 32px;
    }

    @media (max-width: 767.98px) {
        .modern-card-body { padding: 1.5rem; }
        .student-avatar-mobile-marketing { width: 40px; height: 40px; font-size: 1rem; }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize filters
        initializeFilters();

        // Add staggered animation delays
        const cards = document.querySelectorAll('.slide-up');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${(index + 1) * 0.1}s`;
        });

        // Welcome notification removed as per user preference
    });

    function initializeFilters() {
        // Populate filter dropdowns with unique values
        const studentRows = document.querySelectorAll('.student-row-marketing');
        const courses = new Set();
        const names = new Set();
        const genders = new Set();
        const phones = new Set();

        studentRows.forEach(row => {
            courses.add(row.dataset.courseName);
            names.add(row.dataset.name);
            genders.add(row.dataset.gender);
            phones.add(row.dataset.phone);
        });

        // Populate dropdowns
        populateDropdown('courseDropdown', courses);
        populateDropdown('mobileCourseDrop', courses);
        populateDropdown('nameDropdown', names);
        populateDropdown('genderDropdown', genders);
        populateDropdown('mobileGenderDrop', genders);
        populateDropdown('phoneDropdown', phones);
    }

    function populateDropdown(id, values) {
        const dropdown = document.getElementById(id);
        if (dropdown) {
            values.forEach(value => {
                const option = document.createElement('option');
                option.value = value;
                option.textContent = value;
                dropdown.appendChild(option);
            });
        }
    }

    function applyFilters() {
        const course = document.getElementById('courseDropdown').value;
        const name = document.getElementById('nameDropdown').value;
        const gender = document.getElementById('genderDropdown').value;
        const phone = document.getElementById('phoneDropdown').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        filterRows(course, name, gender, phone, startDate, endDate);
    }

    function applyMobileFilters() {
        const course = document.getElementById('mobileCourseDrop').value;
        const gender = document.getElementById('mobileGenderDrop').value;
        const startDate = document.getElementById('mobileStartDate').value;
        const endDate = document.getElementById('mobileEndDate').value;

        filterRows(course, '', gender, '', startDate, endDate);
    }

    function filterRows(course, name, gender, phone, startDate, endDate) {
        const rows = document.querySelectorAll('.student-row-marketing');
        const mobileCards = document.querySelectorAll('.student-card-mobile-marketing');

        [...rows, ...mobileCards].forEach(element => {
            let show = true;

            if (course && element.dataset.courseName !== course) show = false;
            if (name && element.dataset.name !== name) show = false;
            if (gender && element.dataset.gender !== gender) show = false;
            if (phone && element.dataset.phone !== phone) show = false;

            if (startDate || endDate) {
                const dueDate = new Date(element.dataset.dueDate);
                if (startDate && dueDate < new Date(startDate)) show = false;
                if (endDate && dueDate > new Date(endDate)) show = false;
            }

            element.style.display = show ? '' : 'none';
        });

        updateProceedButton();
    }

    function clearMobileFilters() {
        document.getElementById('mobileCourseDrop').value = '';
        document.getElementById('mobileGenderDrop').value = '';
        document.getElementById('mobileStartDate').value = '';
        document.getElementById('mobileEndDate').value = '';
        applyMobileFilters();
    }

    function checkAll() {
        const checkboxes = document.querySelectorAll('input[name="student_checkbox"]');
        const selectAllCheckbox = event.target;

        checkboxes.forEach(checkbox => {
            if (checkbox.closest('tr, .student-card-mobile-marketing').style.display !== 'none') {
                checkbox.checked = selectAllCheckbox.checked;
            }
        });

        updateProceedButton();
    }

    function checkThis(event) {
        event.stopPropagation();
        updateProceedButton();
    }

    function updateProceedButton() {
        const checkedBoxes = document.querySelectorAll('input[name="student_checkbox"]:checked');
        const proceedButton = document.getElementById('proceedButton');
        const proceedButtonMobile = document.getElementById('proceedButtonMobile');

        if (checkedBoxes.length > 0) {
            if (proceedButton) proceedButton.classList.remove('disabled');
            if (proceedButtonMobile) proceedButtonMobile.classList.remove('disabled');
        } else {
            if (proceedButton) proceedButton.classList.add('disabled');
            if (proceedButtonMobile) proceedButtonMobile.classList.add('disabled');
        }
    }

    function sortTable(columnIndex, isDate = false) {
        const table = document.querySelector('.table-glass tbody');
        const rows = Array.from(table.querySelectorAll('tr'));

        rows.sort((a, b) => {
            const aValue = a.cells[columnIndex].textContent.trim();
            const bValue = b.cells[columnIndex].textContent.trim();

            if (isDate) {
                return new Date(aValue) - new Date(bValue);
            }

            return aValue.localeCompare(bValue);
        });

        rows.forEach(row => table.appendChild(row));
    }
</script>
{% endblock %}