{% extends "email_base.html" %}

{% block email_title %}Payment Completed - {{ library_name|default:'Librainian' }}{% endblock %}

{% block email_subject %}Payment Completed - Invoice #{{ invoice.invoice_id }}{% endblock %}

{% block email_description %}Payment completion notification for library services subscription.{% endblock %}

{% block preview_text %}Great news! Your payment of ₹{{ payment.amount_paid }} has been received. Invoice #{{ invoice.invoice_id }} is now fully paid.{% endblock %}

{% block header_icon %}✅{% endblock %}

{% block email_header_title %}Payment Completed{% endblock %}

{% block email_content %}
<h2 class="greeting">Hello {{ student.name }},</h2>
<p class="message">
    Great news! We have successfully received your payment and your invoice is now fully paid.
    Thank you for completing your payment on time.
</p>

<!-- Payment Completion Card -->
<div class="completion-card">
    <div class="completion-icon">
        <i class="fas fa-check-circle"></i>
    </div>
    <h3 class="completion-title">Payment Successfully Completed!</h3>
    <p class="completion-message">Your invoice has been marked as fully paid</p>
</div>

<!-- Payment Summary -->
<div class="payment-summary">
    <h3 class="section-title">💳 Payment Summary</h3>
    
    <div class="summary-grid">
        <div class="summary-item">
            <span class="summary-label">Invoice Number:</span>
            <span class="summary-value">#{{ invoice.invoice_id }}</span>
        </div>
        
        <div class="summary-item">
            <span class="summary-label">Payment Amount:</span>
            <span class="summary-value amount">₹{{ payment.amount_paid }}</span>
        </div>
        
        <div class="summary-item">
            <span class="summary-label">Payment Date:</span>
            <span class="summary-value">{{ payment.payment_date|date:"F j, Y" }}</span>
        </div>
        
        <div class="summary-item">
            <span class="summary-label">Payment Mode:</span>
            <span class="summary-value">{{ payment.payment_mode }}</span>
        </div>
        
        <div class="summary-item">
            <span class="summary-label">Total Invoice Amount:</span>
            <span class="summary-value">₹{{ invoice.total_amount }}</span>
        </div>
        
        <div class="summary-item highlight">
            <span class="summary-label">Status:</span>
            <span class="summary-value status-paid">Fully Paid ✅</span>
        </div>
    </div>
</div>

<!-- Invoice Details -->
<div class="invoice-details">
    <h3 class="section-title">📄 Invoice Details</h3>
    
    <div class="details-grid">
        <div class="detail-item">
            <span class="detail-label">Issue Date:</span>
            <span class="detail-value">{{ invoice.issue_date|date:"F j, Y" }}</span>
        </div>
        
        <div class="detail-item">
            <span class="detail-label">Due Date:</span>
            <span class="detail-value">{{ invoice.due_date|date:"F j, Y" }}</span>
        </div>
        
        <div class="detail-item">
            <span class="detail-label">Services:</span>
            <span class="detail-value">
                {% for shift in invoice.shift.all %}
                    {{ shift.name }}{% if not forloop.last %}, {% endif %}
                {% endfor %}
            </span>
        </div>
        
        <div class="detail-item">
            <span class="detail-label">Period:</span>
            <span class="detail-value">
                {% for month in invoice.months.all %}
                    {{ month.name }}{% if not forloop.last %}, {% endif %}
                {% endfor %}
            </span>
        </div>
    </div>
</div>

<!-- Next Steps -->
<div class="next-steps">
    <h3 class="section-title">🎯 What's Next?</h3>
    <ul class="steps-list">
        <li>Your library access is now confirmed and active</li>
        <li>You can continue using all library services without interruption</li>
        <li>Keep this email as proof of payment for your records</li>
        <li>Contact us if you have any questions about your services</li>
    </ul>
</div>

<!-- Contact Information -->
<div class="contact-info">
    <h3 class="section-title">📞 Need Help?</h3>
    <p>If you have any questions about your payment or services, feel free to contact us:</p>
    <div class="contact-details">
        <p><strong>Email:</strong> <EMAIL></p>
        <p><strong>Phone:</strong> +91-XXXXXXXXXX</p>
        <p><strong>Library:</strong> {{ invoice.student.librarian.library_name }}</p>
    </div>
</div>

<p class="closing-message">
    Thank you for choosing our library services. We appreciate your prompt payment and look forward to serving you!
</p>
{% endblock %}

{% block extra_css %}
<style>
    .completion-card {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        text-align: center;
        padding: 2rem;
        border-radius: 15px;
        margin: 2rem 0;
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
    }

    .completion-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
    }

    .completion-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: white;
    }

    .completion-message {
        font-size: 1rem;
        opacity: 0.9;
        margin: 0;
    }

    .payment-summary {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .summary-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: white;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .summary-item.highlight {
        background: linear-gradient(135deg, #dcfce7, #bbf7d0);
        border-color: #10b981;
    }

    .summary-label {
        font-weight: 500;
        color: #6b7280;
    }

    .summary-value {
        font-weight: 600;
        color: #1f2937;
    }

    .summary-value.amount {
        color: #059669;
        font-size: 1.1rem;
    }

    .status-paid {
        color: #059669 !important;
        font-weight: 700;
    }

    .invoice-details {
        background: #fefefe;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .details-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .detail-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
    }

    .detail-value {
        font-weight: 600;
        color: #1f2937;
    }

    .next-steps {
        background: linear-gradient(135deg, #dbeafe, #bfdbfe);
        border: 1px solid #3b82f6;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .steps-list {
        list-style: none;
        padding: 0;
        margin: 1rem 0 0 0;
    }

    .steps-list li {
        padding: 0.5rem 0;
        padding-left: 2rem;
        position: relative;
        color: #1e40af;
        font-weight: 500;
    }

    .steps-list li:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #059669;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .contact-info {
        background: #f1f5f9;
        border: 1px solid #cbd5e1;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .contact-details {
        margin-top: 1rem;
    }

    .contact-details p {
        margin: 0.5rem 0;
        color: #475569;
    }

    .closing-message {
        text-align: center;
        font-style: italic;
        color: #6b7280;
        margin-top: 2rem;
        padding: 1rem;
        background: #f9fafb;
        border-radius: 8px;
    }

    @media only screen and (max-width: 600px) {
        .summary-grid,
        .details-grid {
            grid-template-columns: 1fr;
        }
        
        .completion-card {
            padding: 1.5rem;
        }
        
        .completion-icon {
            font-size: 3rem;
        }
        
        .completion-title {
            font-size: 1.25rem;
        }
    }
</style>
{% endblock %}
