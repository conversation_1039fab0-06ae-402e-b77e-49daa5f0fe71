{% extends "base.html" %}

{% block title %}Edit Profile - Librainian{% endblock %}

{% block page_title %}Edit Profile{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/{{ role }}/profile/">Profile</a></li>
<li class="breadcrumb-item active" aria-current="page">Edit Profile</li>
{% endblock %}

{% block extra_css %}
<style>
    /* CSS Variables Fallbacks */
    :root {
        --glass-bg-medium: rgba(255, 255, 255, 0.08);
        --glass-border-light: rgba(255, 255, 255, 0.3);
        --glass-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --glass-blur: blur(20px);
        --accent-primary: #6366f1;
    }

    body.dark-mode {
        --glass-bg-medium: rgba(255, 255, 255, 0.08);
        --glass-border-light: rgba(255, 255, 255, 0.15);
        --glass-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
        --accent-primary: #8b5cf6;
    }

    /* Edit Profile Modern Responsive Theme */
    .page-content {
        background: var(--gradient-hero);
        min-height: calc(100vh - var(--topbar-height, 60px));
        padding: 0.5rem;
    }

    .profile-container {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 16px;
        box-shadow: var(--glass-shadow);
        padding: 1rem;
        margin-bottom: 1rem;
        max-width: 1000px;
        margin-left: auto;
        margin-right: auto;
        transition: all 0.3s ease;
    }

    .profile-header {
        background: var(--gradient-hero);
        border-radius: 12px;
        padding: 1.5rem;
        color: var(--text-primary);
        margin-bottom: 1.5rem;
        text-align: center;
        box-shadow: var(--glass-shadow);
        position: relative;
        overflow: hidden;
    }

    .profile-header h1 {
        font-family: 'Comfortaa', sans-serif;
        font-size: clamp(1.5rem, 4vw, 2rem);
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 1;
        color: var(--text-primary);
    }

    .profile-header p {
        font-size: clamp(0.875rem, 3vw, 1.125rem);
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
        color: var(--text-secondary);
    }

    .form-section {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--glass-shadow);
        transition: all 0.3s ease;
    }

    .form-section:hover {
        transform: translateY(-2px);
        box-shadow: var(--glass-shadow-lg);
    }

    .section-title {
        font-family: 'Comfortaa', sans-serif;
        font-size: clamp(1.125rem, 3vw, 1.25rem);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--glass-border);
    }

    .section-title i {
        color: var(--accent-primary);
        font-size: clamp(1rem, 2.5vw, 1.125rem);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: block;
        font-size: clamp(0.875rem, 2.5vw, 1rem);
    }

    .required-label::after {
        content: " *";
        color: #ef4444;
        font-size: 0.875rem;
        font-weight: 700;
    }

    .form-control {
        background: var(--glass-bg);
        border: 2px solid var(--glass-border);
        border-radius: 12px;
        padding: clamp(0.5rem, 2vw, 0.75rem) 1rem;
        font-size: clamp(0.875rem, 2.5vw, 1rem);
        transition: all 0.3s ease;
        width: 100%;
        color: var(--text-primary);
    }

    .form-control:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background: var(--glass-bg-medium);
    }

    .form-control::placeholder {
        color: var(--text-muted);
    }

    .form-select {
        background: var(--glass-bg);
        border: 2px solid var(--glass-border);
        border-radius: 12px;
        padding: clamp(0.5rem, 2vw, 0.75rem) 1rem;
        font-size: clamp(0.875rem, 2.5vw, 1rem);
        transition: all 0.3s ease;
        width: 100%;
        cursor: pointer;
        color: var(--text-primary);
    }

    .form-select:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background: var(--glass-bg-medium);
    }

    .input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .input-group-text {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        font-size: clamp(0.875rem, 2.5vw, 1rem);
        z-index: 2;
        background: none;
        border: none;
        pointer-events: none;
    }

    .input-group .form-control,
    .input-group .form-select {
        padding-left: clamp(2rem, 5vw, 2.5rem);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .image-upload-container {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .image-preview {
        width: 100%;
        height: clamp(150px, 25vw, 200px);
        background: var(--glass-bg);
        border: 2px dashed var(--glass-border);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
    }

    .image-preview:hover {
        border-color: var(--accent-primary);
        background: var(--glass-bg-medium);
        transform: translateY(-2px);
    }

    .image-preview img {
        max-width: 100%;
        max-height: 100%;
        object-fit: cover;
        border-radius: 12px;
    }

    .image-placeholder {
        text-align: center;
        color: var(--text-muted);
    }

    .image-placeholder i {
        font-size: clamp(2rem, 6vw, 3rem);
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .image-placeholder p {
        margin: 0;
        font-weight: 500;
        font-size: clamp(0.875rem, 2.5vw, 1rem);
    }

    .file-input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .btn-primary {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border: none;
        border-radius: 12px;
        padding: clamp(0.75rem, 2vw, 0.875rem) clamp(1.5rem, 4vw, 2rem);
        color: white;
        font-weight: 600;
        font-size: clamp(0.875rem, 2.5vw, 1.1rem);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
        width: 100%;
        max-width: 200px;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        color: white;
    }

    .btn-secondary {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: clamp(0.75rem, 2vw, 0.875rem) clamp(1.5rem, 4vw, 2rem);
        color: var(--text-primary);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: var(--glass-shadow);
        font-size: clamp(0.875rem, 2.5vw, 1rem);
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }

    .btn-secondary:hover {
        color: var(--text-primary);
        transform: translateY(-2px);
        box-shadow: var(--glass-shadow-lg);
        background: var(--glass-bg-medium);
        text-decoration: none;
    }

    .form-actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid var(--glass-border);
    }

    .help-text {
        font-size: clamp(0.75rem, 2vw, 0.875rem);
        color: var(--text-muted);
        margin-top: 0.25rem;
        line-height: 1.4;
    }

    .invalid-feedback {
        display: block;
        font-size: clamp(0.75rem, 2vw, 0.875rem);
        color: #ef4444;
        margin-top: 0.25rem;
    }

    /* Responsive Design - Mobile First */

    /* Small devices (landscape phones, 576px and up) */
    @media (min-width: 576px) {
        .page-content {
            padding: 0.75rem;
        }

        .profile-container {
            padding: 1.25rem;
        }

        .form-row {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .form-actions {
            flex-direction: row;
            justify-content: space-between;
        }

        .btn-primary,
        .btn-secondary {
            width: auto;
            min-width: 150px;
        }
    }

    /* Medium devices (tablets, 768px and up) */
    @media (min-width: 768px) {
        .page-content {
            padding: 1rem;
        }

        .profile-container {
            padding: 2rem;
        }

        .form-section {
            padding: 2rem;
        }

        .profile-header {
            padding: 2rem;
        }
    }

    /* Large devices (desktops, 992px and up) */
    @media (min-width: 992px) {
        .page-content {
            padding: 1.5rem;
        }

        .form-row {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }
    }

    /* Extra large devices (large desktops, 1200px and up) */
    @media (min-width: 1200px) {
        .page-content {
            padding: 2rem;
        }

        .profile-container {
            padding: 2.5rem;
        }
    }

    /* Mobile specific adjustments */
    @media (max-width: 575.98px) {
        .page-content {
            padding-bottom: calc(0.5rem + var(--bottom-menu-height, 80px));
        }

        .profile-header h1 {
            line-height: 1.2;
        }

        .section-title {
            flex-wrap: wrap;
            gap: 0.25rem;
        }

        .input-group .form-control,
        .input-group .form-select {
            padding-left: 2rem;
        }

        .input-group-text {
            left: 0.75rem;
            font-size: 0.875rem;
        }
    }

    /* Dark Mode Specific Enhancements */
    body.dark-mode .form-section:hover {
        border-color: var(--glass-border-light);
    }

    body.dark-mode .image-preview:hover {
        border-color: var(--accent-primary);
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.2);
    }

    body.dark-mode .btn-secondary:hover {
        border-color: var(--glass-border-light);
    }

    /* Animation */
    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Toggle Switch Styles */
    .toggle-container {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
        margin: 0;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--glass-bg-medium);
        border: 1px solid var(--glass-border-light);
        transition: 0.3s;
        border-radius: 24px;
        backdrop-filter: blur(10px);
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: 0.3s;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    input:checked + .toggle-slider {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: rgba(255, 255, 255, 0.5);
    }

    input:checked + .toggle-slider:before {
        transform: translateX(26px);
    }

    .toggle-label {
        color: var(--text-primary, #333);
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Dark mode support */
    body.dark-mode .toggle-label {
        color: var(--text-primary-dark, #fff);
    }

    body.dark-mode .toggle-slider:before {
        background-color: #f0f0f0;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-content">
    <div class="container-fluid px-1 px-sm-2 px-md-3 px-lg-4">
        <div class="profile-container fade-in">
    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    {% endif %}

    <form method="POST" enctype="multipart/form-data" id="profileForm" novalidate>
        {% csrf_token %}

        <!-- Basic Information Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-user"></i>
                Basic Information
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="first_name" class="form-label required-label">First Name</label>
                    <div class="input-group">
                        <i class="fas fa-user input-group-text"></i>
                        <input type="text" name="first_name" id="first_name" class="form-control"
                               value="{{ librarian.first_name }}" required maxlength="30">
                    </div>
                    <div class="help-text">Enter your first name</div>
                </div>

                <div class="form-group">
                    <label for="last_name" class="form-label required-label">Last Name</label>
                    <div class="input-group">
                        <i class="fas fa-user input-group-text"></i>
                        <input type="text" name="last_name" id="last_name" class="form-control"
                               value="{{ librarian.last_name }}" required maxlength="30">
                    </div>
                    <div class="help-text">Enter your last name</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="email" class="form-label required-label">Email Address</label>
                    <div class="input-group">
                        <i class="fas fa-envelope input-group-text"></i>
                        <input type="email" name="email" id="email" class="form-control"
                               value="{{ librarian.email }}" required>
                    </div>
                    <div class="help-text">Your email address for notifications</div>
                </div>

                <div class="form-group">
                    <label for="username_display" class="form-label">Username</label>
                    <div class="input-group">
                        <i class="fas fa-at input-group-text"></i>
                        <input type="hidden" name="username" value="{{ librarian.username }}">
                            <input type="text" id="username_display" class="form-control"
                                value="{{ librarian.username }}" readonly disabled style="background-color: #ffd3ff;">
                    </div>
                    <div class="help-text">Username cannot be changed</div>
                </div>
            </div>
        </div>

        <!-- Profile Image Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-image"></i>
                Profile Image
            </div>

            <div class="image-upload-container">
                <div class="image-preview" onclick="document.getElementById('profile_image').click()">
                    {% if profile.image %}
                        <img src="{{ profile.image.url }}" alt="Profile Image" id="imagePreview">
                    {% else %}
                        <div class="image-placeholder" id="imagePlaceholder">
                            <i class="fas fa-camera"></i>
                            <p>Click to upload profile image</p>
                        </div>
                    {% endif %}
                    <input type="file" name="image" id="profile_image" class="file-input"
                           accept="image/*" onchange="previewImage(this)">
                </div>
                <div class="help-text">Upload a profile image (JPG, PNG, max 5MB)</div>
            </div>
        </div>

        <!-- Role-Specific Information -->
        {% if role == "librarian" %}
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-building"></i>
                Library Information
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="library_name" class="form-label required-label">Library Name</label>
                    <div class="input-group">
                        <i class="fas fa-book input-group-text"></i>
                        <input type="text" name="libraryname" id="library_name" class="form-control"
                               value="{{ profile.library_name }}" required maxlength="100">
                    </div>
                    <div class="help-text">Official name of your library</div>
                </div>

                <div class="form-group">
                    <label for="phone" class="form-label required-label">Phone Number</label>
                    <div class="input-group">
                        <i class="fas fa-phone input-group-text"></i>
                        <input type="tel" name="phone" id="phone" class="form-control"
                               value="{{ profile.librarian_phone_num }}" required pattern="[0-9]{10}">
                    </div>
                    <div class="help-text">10-digit phone number</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="address" class="form-label required-label">Address</label>
                    <div class="input-group">
                        <i class="fas fa-map-marker-alt input-group-text"></i>
                        <textarea name="address" id="address" class="form-control" rows="3"
                                  required>{{ profile.librarian_address }}</textarea>
                    </div>
                    <div class="help-text">Complete library address</div>
                </div>

                <div class="form-group">
                    <label for="google_map" class="form-label required-label">Google Map Link</label>
                    <div class="input-group">
                        <i class="fas fa-map input-group-text"></i>
                        <input type="url" name="google_map" id="google_map" class="form-control"
                               value="{{ profile.google_map_url }}" required>
                    </div>
                    <div class="help-text">Google Maps URL for your library location</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="discount" class="form-label">Discount (%)</label>
                    <div class="input-group">
                        <i class="fas fa-percent input-group-text"></i>
                        <input type="number" name="discount" id="discount" class="form-control"
                               value="{{ profile.discount_amount }}" min="0" max="100" step="0.01">
                    </div>
                    <div class="help-text">Default discount percentage (0-100)</div>
                </div>

                <div class="form-group">
                    <label for="description" class="form-label required-label">Description</label>
                    <div class="input-group">
                        <i class="fas fa-info-circle input-group-text"></i>
                        <textarea name="description" id="description" class="form-control" rows="3"
                                  required maxlength="500">{{ profile.description }}</textarea>
                    </div>
                    <div class="help-text">Brief description of your library</div>
                </div>
            </div>
        </div>

        <!-- Registration Settings Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-cogs"></i>
                Registration Settings
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Security Deposit</label>
                    <div class="toggle-container">
                        <label class="toggle-switch">
                            <input type="checkbox" name="security_deposit_enabled" id="security_deposit_toggle"
                                   {% if profile.security_deposit_enabled %}checked{% endif %}
                                   onchange="toggleSecurityDeposit()">
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">Enable security deposit requirement</span>
                    </div>
                    <div class="help-text">When enabled, students will be required to pay a security deposit</div>
                </div>

                <div class="form-group" id="security_deposit_amount_group"
                     style="display: {% if profile.security_deposit_enabled %}block{% else %}none{% endif %};">
                    <label for="security_deposit_amount" class="form-label">Security Deposit Amount</label>
                    <div class="input-group">
                        <i class="fas fa-rupee-sign input-group-text"></i>
                        <input type="number" name="security_deposit_amount" id="security_deposit_amount"
                               class="form-control" value="{{ profile.security_deposit_amount }}"
                               min="0" max="9999" step="1">
                    </div>
                    <div class="help-text">Amount in rupees (maximum 9999)</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Proof of Identity</label>
                    <div class="toggle-container">
                        <label class="toggle-switch">
                            <input type="checkbox" name="proof_of_identity_enabled" id="proof_of_identity_toggle"
                                   {% if profile.proof_of_identity_enabled %}checked{% endif %}>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">Require proof of identity during registration</span>
                    </div>
                    <div class="help-text">When enabled, students must provide identity document details during registration</div>
                </div>
            </div>
        </div>
        {% endif %}

        {% if role == "sublibrarian" %}
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-user-tie"></i>
                Sublibrarian Information
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="phone" class="form-label required-label">Phone Number</label>
                    <div class="input-group">
                        <i class="fas fa-phone input-group-text"></i>
                        <input type="tel" name="phone" id="phone" class="form-control"
                               value="{{ profile.sublibrarian_phone_num }}" required pattern="[0-9]{10}">
                    </div>
                    <div class="help-text">10-digit phone number</div>
                </div>

                <div class="form-group">
                    <label for="address" class="form-label required-label">Address</label>
                    <div class="input-group">
                        <i class="fas fa-map-marker-alt input-group-text"></i>
                        <textarea name="address" id="address" class="form-control" rows="3"
                                  required>{{ profile.sublibrarian_address }}</textarea>
                    </div>
                    <div class="help-text">Your residential address</div>
                </div>
            </div>
        </div>
        {% endif %}

        {% if role == "librarycommander" %}
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-crown"></i>
                Library Commander Information
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="phone" class="form-label required-label">Phone Number</label>
                    <div class="input-group">
                        <i class="fas fa-phone input-group-text"></i>
                        <input type="tel" name="phone" id="phone" class="form-control"
                               value="{{ profile.librarycommander_phone_num }}" required pattern="[0-9]{10}">
                    </div>
                    <div class="help-text">10-digit phone number</div>
                </div>

                <div class="form-group">
                    <label for="address" class="form-label required-label">Address</label>
                    <div class="input-group">
                        <i class="fas fa-map-marker-alt input-group-text"></i>
                        <textarea name="address" id="address" class="form-control" rows="3"
                                  required>{{ profile.librarycommander_address }}</textarea>
                    </div>
                    <div class="help-text">Your residential address</div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="/{{ role }}/profile/" class="btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Cancel
            </a>
            <button type="submit" class="btn-primary" id="submitBtn">
                <i class="fas fa-save"></i>
                Update
            </button>
        </div>
    </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced Profile Edit Form
    class ProfileEditor {
        constructor() {
            this.form = document.getElementById('profileForm');
            this.submitBtn = document.getElementById('submitBtn');
            this.init();
        }

        init() {
            this.setupValidation();
            this.setupImagePreview();
            this.setupFormSubmission();
        }

        setupValidation() {
            // Real-time validation
            const inputs = this.form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearErrors(input));
            });

            // Phone number formatting
            const phoneInputs = this.form.querySelectorAll('input[type="tel"]');
            phoneInputs.forEach(input => {
                input.addEventListener('input', (e) => this.formatPhoneNumber(e.target));
            });
        }

        validateField(field) {
            const value = field.value.trim();
            let isValid = true;
            let errorMessage = '';

            // Required field validation
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                errorMessage = 'This field is required.';
            }

            // Email validation
            if (field.type === 'email' && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address.';
                }
            }

            // Phone validation
            if (field.type === 'tel' && value) {
                const phoneRegex = /^[0-9]{10}$/;
                if (!phoneRegex.test(value.replace(/\D/g, ''))) {
                    isValid = false;
                    errorMessage = 'Please enter a valid 10-digit phone number.';
                }
            }

            // URL validation
            if (field.type === 'url' && value) {
                try {
                    new URL(value);
                } catch {
                    isValid = false;
                    errorMessage = 'Please enter a valid URL.';
                }
            }

            this.showFieldError(field, isValid ? '' : errorMessage);
            return isValid;
        }

        showFieldError(field, message) {
            // Remove existing error
            const existingError = field.parentNode.parentNode.querySelector('.invalid-feedback');
            if (existingError) {
                existingError.remove();
            }

            if (message) {
                field.classList.add('is-invalid');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = message;
                field.parentNode.parentNode.appendChild(errorDiv);
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        }

        clearErrors(field) {
            field.classList.remove('is-invalid', 'is-valid');
            const errorDiv = field.parentNode.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        }

        formatPhoneNumber(input) {
            let value = input.value.replace(/\D/g, '');
            if (value.length > 10) {
                value = value.substring(0, 10);
            }
            input.value = value;
        }

        setupImagePreview() {
            // Image preview functionality is handled by the global previewImage function
        }

        setupFormSubmission() {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();

                // Validate all fields
                const inputs = this.form.querySelectorAll('input[required], textarea[required], select[required]');
                let isFormValid = true;

                inputs.forEach(input => {
                    if (!this.validateField(input)) {
                        isFormValid = false;
                    }
                });

                if (isFormValid) {
                    this.submitForm();
                } else {
                    this.showNotification('Please fix the errors before submitting.', 'error');
                    // Scroll to first error
                    const firstError = this.form.querySelector('.is-invalid');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
            });
        }

        submitForm() {
            // Show loading state
            const originalText = this.submitBtn.innerHTML;
            this.submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            this.submitBtn.disabled = true;

            // Submit the form
            this.form.submit();
        }

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    // Global image preview function
    function previewImage(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                const preview = document.getElementById('imagePreview');
                const placeholder = document.getElementById('imagePlaceholder');

                if (preview) {
                    preview.src = e.target.result;
                } else {
                    // Create new image element
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.alt = 'Profile Image';
                    img.id = 'imagePreview';

                    // Replace placeholder
                    const container = input.parentNode;
                    if (placeholder) {
                        placeholder.remove();
                    }
                    container.appendChild(img);
                }
            };

            reader.readAsDataURL(input.files[0]);
        }
    }

    // Toggle security deposit amount field
    function toggleSecurityDeposit() {
        const toggle = document.getElementById('security_deposit_toggle');
        const amountGroup = document.getElementById('security_deposit_amount_group');
        const amountInput = document.getElementById('security_deposit_amount');

        if (toggle.checked) {
            amountGroup.style.display = 'block';
            amountInput.required = true;
        } else {
            amountGroup.style.display = 'none';
            amountInput.required = false;
            amountInput.value = '0';
        }
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        window.profileEditor = new ProfileEditor();

        // Initialize security deposit toggle state
        toggleSecurityDeposit();
    });
</script>
{% endblock %}
