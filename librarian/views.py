from django.shortcuts import get_object_or_404, render, redirect
from django.contrib import messages
from django.contrib.auth.models import Group
from django.contrib.auth import logout
from django.db import IntegrityError
from django.core.mail import send_mail
import os
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from membership.models import *
from studentsData.utils import update_seat_availability
from .models import *
from studentsData.models import *
from manager.models import *
from visitorsData.models import *
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
from django.contrib.auth.decorators import login_required
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
import random
import string
import re
import json
from django.db.models import Count
from django.db.models.functions import Trunc<PERSON>onth
from threading import Thread
from django.utils.crypto import get_random_string
import threading
from django.urls import reverse
from django.core.mail import EmailMessage
from django.db.models import Sum, Count, Q
from django.db.models.functions import TruncMonth, TruncDate
from django.http import HttpResponse, JsonResponse
from django.utils.timezone import now
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
import json
from Library.views import send_sms
from django.template.loader import render_to_string
from django.conf import settings
from django.db import connection
from django.core.exceptions import ObjectDoesNotExist
from membership.decorators import membership_required
from wallet_and_transactions.models import *
from Library.user_auth import *
import threading
from django.db import IntegrityError
from smtplib import SMTPException
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from studentsData.views import send_dynamic_email
from django.db.models import Prefetch
from django.shortcuts import redirect
from allauth.socialaccount.models import SocialAccount
from django.contrib.auth.models import User, Group
from django.contrib import messages
from datetime import timedelta
from django.utils import timezone

from django.shortcuts import render, get_object_or_404
import qrcode
from io import BytesIO
import base64
from django.views.decorators.http import require_http_methods
from blogs.models import PageCounter
from django.http import HttpResponse
from utils.notifications import template_content, send_bulk_sms

def librarian_signup(request, *args, **kwargs):
    if request.method == "POST":
        try:
            manager_id = request.POST.get("manager")
            library_name = request.POST.get("libraryname").title()
            phone = request.POST.get("phone")
            address = request.POST.get("address")
            email = request.POST.get("email")
            first_name = request.POST.get("first_name")
            last_name = request.POST.get("last_name")
            username = request.POST.get("username")
            password1 = request.POST.get("password1")
            password2 = request.POST.get("password2")
            google_map_url = request.POST.get("google_map_url")

            # Validate username format - only allow letters, numbers, periods, hyphens, underscores
            if not re.match(r'^[a-zA-Z0-9._-]+$', username):
                messages.error(request, "Username can only contain letters, numbers, periods (.), hyphens (-), and underscores (_).")
                return redirect("lib_signup")

            # Validate passwords
            if password1 != password2:
                messages.error(request, "Passwords do not match.")
                return redirect("lib_signup")

            # Check for existing user
            if User.objects.filter(username=username).exists():
                messages.error(request, "Username already taken.")
                return redirect("lib_signup")

            if User.objects.filter(email=email).exists():
                messages.error(request, "Email already taken.")
                return redirect("lib_signup")

            # Generate OTP
            otp = get_random_string(length=6, allowed_chars="0123456789")
            request.session["signup_otp"] = otp
            request.session["signup_data"] = {
                "manager_id": manager_id,
                "library_name": library_name,
                "phone": phone,
                "address": address,
                "email": email,
                "first_name": first_name,
                "last_name": last_name,
                "username": username,
                "password1": password1,
                "google_map_url": google_map_url,
            }

            # Send OTP via email using threading
            email_thread = threading.Thread(target=send_otp_email, args=(email, otp))
            email_thread.start()

            # Send Sms
            sender = "LBRIAN"
            number = phone
            message = f"Hi {library_name}, Thank you so much for choosing Librainian App - Your partner in growth, Your OTP to register you Library is: {otp}. PNKVEN"
            template_id = 165731

            send_sms(str(sender), str(number), str(message), str(template_id))

            messages.info(request, f"OTP sent to {email} and {phone}. Please verify.")

            return redirect("/librarian/verify-otp/")

        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")

    managers = Manager_param.objects.all()
    return render(request, "signup.html", {"role": "librarian", "managers": managers})



def send_otp_email(to_email, otp):
    try:
        subject = "OTP for Librarian Signup Verification"
        from_email = settings.EMAIL_HOST_USER or settings.DEFAULT_FROM_EMAIL

        html_content = render_to_string("email_otp.html", {"otp": otp})
        text_content = strip_tags(html_content)

        msg = EmailMultiAlternatives(subject, text_content, from_email, [to_email])
        msg.attach_alternative(html_content, "text/html")
        msg.send()
    except Exception as e:
        print(f"Error sending email: {e}")



def resend_otp(request):
    # Retrieve OTP and details from the session
    otp = request.session.get("signup_otp")
    signup_data = request.session.get("signup_data")

    # Check if session data exists
    if not otp or not signup_data:
        messages.error(
            request, "Session expired or invalid data. Please restart the signup process."
        )
        return redirect("lib_signup")

    # Safely extract data with defaults
    email = signup_data.get("email")
    phone = signup_data.get("phone")
    library_name = signup_data.get("library_name")

    # Validate required fields
    if not otp or not email or not phone or not library_name:
        messages.error(
            request, "Session expired or missing data. Please restart the signup process."
        )
        return redirect("lib_signup")

    try:
        # Send email in thread
        email_thread = threading.Thread(target=send_otp_email, args=(email, otp))
        email_thread.start()

        # Send SMS
        sender = "LBRIAN"
        message = f"Hi {library_name}, Thank you so much for choosing Librainian App - Your partner in growth, Your OTP to register you Library is: {otp}. PNKVEN"
        template_id = 165731

        send_sms(str(sender), str(phone), str(message), str(template_id))

        messages.success(request, f"OTP resent to {email} and {phone}. Please verify.")
        return redirect("/librarian/verify-otp/")

    except Exception as e:
        messages.error(request, f"Failed to resend OTP: {str(e)}")
        return redirect("/librarian/verify-otp/")


def verify_otp(request):
    if request.method == "POST":
        user_otp = request.POST.get("otp")
        saved_otp = request.session.get("signup_otp")
        signup_data = request.session.get("signup_data")

        # Check if session data exists
        if not saved_otp or not signup_data:
            messages.error(request, "Session expired. Please restart the signup process.")
            return redirect("lib_signup")

        manager_id = signup_data.get("manager_id")
        if not manager_id:
            messages.error(request, "Invalid session data. Please restart the signup process.")
            return redirect("lib_signup")

        try:
            manager = Manager_param.objects.get(id=manager_id)
        except Manager_param.DoesNotExist:
            messages.error(request, "Invalid manager. Please restart the signup process.")
            return redirect("lib_signup")

        if user_otp == saved_otp:
            try:
                # Create librarian user
                librarian = User.objects.create_user(
                    first_name=signup_data["first_name"],
                    last_name=signup_data["last_name"],
                    username=signup_data["username"],
                    email=signup_data["email"],
                    password=signup_data["password1"],
                )

                if librarian:
                    # Create Librarian_param object
                    librarian_main = Librarian_param.objects.create(
                        user=librarian,
                        manager=manager,
                        library_name=signup_data["library_name"],
                        librarian_phone_num=signup_data["phone"],
                        librarian_address=signup_data["address"],
                        google_map_url=signup_data["google_map_url"],
                    )

                    group, created = Group.objects.get_or_create(name="librarian")
                    librarian.groups.add(group)

                    notify_manager_for_approval(request, librarian_main, signup_data)

                    membership = Membership.objects.filter(librarian=librarian_main)

                    if not membership:
                        # Use utility function to get or create free plan
                        free_plan = get_or_create_free_plan()
                        start_date = timezone.now().date()
                        expiry_date = start_date + timedelta(days=10)

                        Membership.objects.create(
                            plan=free_plan,
                            librarian=librarian_main,
                            start_date=start_date,
                            expiry_date=expiry_date,
                        )
                    wallet = Wallet.objects.filter(user=librarian)

                    if not wallet:
                        wallet = Wallet.objects.create(user=librarian)

                    del request.session["signup_otp"]
                    del request.session["signup_data"]

                    messages.success(
                        request,
                        "Librarian signup successful. Sign in to continue.",
                    )
                    return redirect("/librarian/login/")

                else:
                    messages.error(request, "Failed to create user. Please try again.")

            except Exception as e:
                messages.error(request, f"An error occurred: {str(e)}")
                return redirect("lib_signup")

        else:
            messages.error(request, "Invalid OTP. Please try again.")

    return render(request, "auth_verify_otp.html")


def send_manager_approval_email(
    manager_email, librarian_main, signup_data, approval_url
):
    try:
        subject = "Librarian Signup Approval"
        from_email = settings.EMAIL_HOST_USER or settings.DEFAULT_FROM_EMAIL
        to_email = manager_email

        html_content = render_to_string(
            "templates/signup_approval_email.html",
            {
                "librarian": librarian_main,
                "manager": manager_email,
                "signup_data": signup_data,
                "approval_url": approval_url,
            },
        )
        text_content = strip_tags(html_content)

        msg = EmailMultiAlternatives(subject, text_content, from_email, [to_email])
        msg.attach_alternative(html_content, "text/html")
        msg.send()
    except Exception as e:
        print(f"Error sending approval email: {str(e)}")


def notify_manager_for_approval(request, librarian_main, signup_data):
    try:
        manager = Manager_param.objects.get(id=signup_data["manager_id"])
        manager_email = manager.user.email

        # Construct the absolute URL
        approval_url = request.build_absolute_uri(
            reverse("librarian-approval", kwargs={"slug": librarian_main.slug})
        )

        # Send approval email using threading
        email_thread = threading.Thread(
            target=send_manager_approval_email,
            args=(manager_email, librarian_main, signup_data, approval_url),
        )
        email_thread.start()

    except Exception as e:
        print(f"Error sending approval email: {str(e)}")


# def google_signup_login(request):
#     if request.user.is_authenticated:
#         user = request.user

#         # Check if Librarian_param already exists for the user
#         if not Librarian_param.objects.filter(user=user).exists():
#             # Here, you could use default values or fetch data from the Google profile
#             manager = Manager_param.objects.first()  # Example: default manager
#             Librarian_param.objects.create(
#                 user=user,
#                 manager=manager,
#                 library_name="Default Library",
#                 librarian_phone_num="1234567890",  # Example phone, customize as needed
#                 librarian_address="Default Address",
#             )

#             # Add user to librarian group if not already added
#             group, created = Group.objects.get_or_create(name="librarian")
#             user.groups.add(group)

#         # Check if a Membership entry exists, and create a free plan if not
#         if not Membership.objects.filter(librarian=user).exists():
#             free_plan = Plan.objects.filter(price=0).first()
#             if free_plan:
#                 Membership.objects.create(
#                     plan=free_plan,
#                     librarian=Librarian_param.objects.get(user=user),
#                     start_date=timezone.now().date(),
#                     expiry_date=timezone.now().date() + timedelta(days=10),
#                 )

#         # Check if a Wallet exists for the user, create if not
#         if not Wallet.objects.filter(user=user).exists():
#             Wallet.objects.create(user=user)

#         messages.success(request, "Logged in successfully with Google.")
#         return redirect(
#             "lib_login"
#         )  # Replace with the URL of the page you want to redirect to

#     else:
#         messages.error(request, "Login with Google failed.")
#         return redirect("lib_signup")


def librarian_login(request, *args, **kwargs):
    if request.method == "POST":
        try:
            librarian = authenticate_and_login(request, "librarian")

            if librarian:
                # Check if this is the first login (handle None last_login)
                if librarian.last_login and librarian.last_login.date() == librarian.date_joined.date():
                    return redirect("/librarian/shifts/")
                elif not librarian.last_login:
                    # First time login
                    return redirect("/librarian/shifts/")
                return redirect("/librarian/dashboard/")
            else:
                messages.error(
                    request, "Invalid credentials or you do not have librarian access."
                )
                return redirect("lib_login")
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")

    return render(request, "login.html", {"role": "librarian"})


@login_required(login_url="/librarian/login/")
def librarian_logout(request, *args, **kwargs):
    try:
        logout(request)
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")

    return redirect("/")


@login_required(login_url="/librarian/login/")  # type: ignore
def librarian_profile(request, *args, **kwargs):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    qr_code = generate_qr_code(librarian)
    if user.is_authenticated and librarian.is_librarian:
        try:
            lib_profile = Librarian_param.objects.get(user=request.user)
            # Fetching Membership
            try:
                membership = Membership.objects.get(librarian=lib_profile)
            except Membership.DoesNotExist:
                membership = None

            # Fetching Sublibrarian
            try:
                sublibrarian = Sublibrarian_param.objects.filter(librarian=lib_profile)

            except Sublibrarian_param.DoesNotExist:
                sublibrarian = None

            # Fetching Wallet
            try:
                wallet = Wallet.objects.get(user=user)

            except Wallet.DoesNotExist:
                wallet = None

            try:
                transaction = Transaction.objects.filter(wallet=wallet)[::-1][:5]

            except Transaction.DoesNotExist:
                transaction = None

            return render(
                request,
                "user_profile.html",
                {
                    "lib": lib_profile,
                    "role": "librarian",
                    "membership": membership,
                    "sublibrarian": sublibrarian,
                    "wallet": wallet,
                    "transaction": transaction,
                    "qr_code": qr_code,
                },
            )
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")
            return redirect("/librarian/login/")
    else:
        return render(request, "unauthorized.html", {"role": "librarian"})


@login_required(login_url="/librarian/login/")  # type: ignore
def edit_librarian_profile(request):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    if user.is_authenticated and librarian.is_librarian:
        try:
            # Fetch the logged-in user and their librarian profile
            librarian = request.user
            librarian_profile = Librarian_param.objects.get(user=librarian)

            if request.method == "POST":
                # Get data from the form for the User model
                first_name = request.POST.get("first_name")
                last_name = request.POST.get("last_name")
                email = request.POST.get("email")

                # Get data for the Librarian_param model
                libraryname = request.POST.get("libraryname")
                phone = request.POST.get("phone")
                address = request.POST.get("address")
                google_map_url = request.POST.get("google_map")
                discount_amount = request.POST.get("discount", 0)
                description = request.POST.get("description", None)
                image = request.FILES.get("image", None)

                if image:
                    if librarian_profile.image:  # Check if there's an existing image
                        old_image_path = librarian_profile.image.path
                        if os.path.exists(old_image_path):
                            os.remove(old_image_path)  # Remove the old image
                    librarian_profile.image = image

                if discount_amount:
                    librarian_profile.discount_available = True
                    librarian_profile.discount_amount = discount_amount

                try:
                    librarian.first_name = first_name
                    librarian.last_name = last_name
                    librarian.email = email
                    username = request.POST.get("username")
                    if username:
                        librarian.username = username
                    librarian.save()

                    # Update the Librarian_param model fields
                    librarian_profile.library_name = libraryname
                    librarian_profile.librarian_phone_num = phone
                    librarian_profile.librarian_address = address
                    librarian_profile.google_map_url = google_map_url
                    librarian_profile.description = description
                    librarian_profile.save()

                    messages.success(request, "Profile updated successfully.")
                    return redirect("/librarian/profile/")  # Redirect to profile page

                except IntegrityError as e:
                    error_message = str(e).lower()
                    if "unique constraint" in error_message or "duplicate" in error_message:
                        if "email" in error_message:
                            messages.error(request, "A user with this email already exists.")
                        elif "username" in error_message:
                            messages.error(request, "A user with this username already exists.")
                        else:
                            messages.error(request, "A user with this information already exists.")
                    else:
                        messages.error(request, "Database error occurred. Please try again.")

                    # Return to the form with current data
                    return render(
                        request,
                        "edit_profile.html",
                        {
                            "profile": librarian_profile,
                            "librarian": librarian,
                            "role": "librarian",
                        },
                    )

            # Prepopulate the form with current data
            return render(
                request,
                "edit_profile.html",
                {
                    "profile": librarian_profile,
                    "librarian": librarian,
                    "role": "librarian",
                },
            )
        except Librarian_param.DoesNotExist:
            messages.error(request, "Librarian profile not found.")
            return redirect(
                "/librarian/login/"
            )  # Redirect to login if no profile is found
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")
            return redirect("/librarian/edit-profile/")


@login_required(login_url="/librarian/login/")
# @membership_required("Basic")
def library_data(request, *args, **kwargs):
    user = request.user
    librarian = None
    sublibrarian = None
    students = StudentData.objects.none()

    try:
        sublibrarian = Sublibrarian_param.objects.get(user=user)
        librarian = sublibrarian.librarian
    except Sublibrarian_param.DoesNotExist:
        librarian = Librarian_param.objects.get(user=user)

    location = librarian.librarian_address if librarian else None

    if user.is_authenticated and librarian.is_librarian:
        if sublibrarian:
            students = StudentData.objects.filter(sublibrarian=sublibrarian)
        else:
            sublibrarians = Sublibrarian_param.objects.filter(librarian=librarian)
            sublibrarian_students = StudentData.objects.filter(
                sublibrarian__in=sublibrarians
            )
            librarian_students = StudentData.objects.filter(librarian=librarian)
            students = sublibrarian_students | librarian_students

        students_with_unpaid_fees = students.select_related("registrationfee").filter(
            registrationfee__is_paid=False
        )

        invoices = Invoice.objects.filter(student__in=students).select_related(
            "student"
        )

        invoice_data = {}
        for invoice in invoices:
            invoice_data[invoice.student_id] = {  # type: ignore
                "invoice": invoice,
                "shifts": invoice.shift.all(),
                "months": invoice.months.all(),
            }

        return render(
            request,
            "table.html",
            {
                "students": students,
                "students_with_fees": students_with_unpaid_fees,
                "invoice_data": invoice_data,
                "role": "librarian",
                "location": location,
                "librarian": librarian,
            },
        )
    else:
        return render(request, "unauthorized.html", {"role": "librarian"})


@login_required(login_url="/librarian/login/")
@membership_required("Standard")
def marketing_wise_data(request, *args, **kwargs):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    location = librarian.librarian_address

    if user.is_authenticated and librarian.is_librarian:

        try:
            sublibrarian = Sublibrarian_param.objects.get(user=user)
            students = StudentData.objects.filter(sublibrarian=sublibrarian)

        except Sublibrarian_param.DoesNotExist:
            librarian = Librarian_param.objects.get(user=user)
            students = StudentData.objects.filter(librarian=librarian)

        student_data = []

        for student in students:
            try:
                registration_fee_status = RegistrationFee.objects.get(student=student)
            except RegistrationFee.DoesNotExist:
                registration_fee_status = None

            invoice_data = Invoice.objects.filter(student=student).last()

            if invoice_data:
                shifts = invoice_data.shift.all()
                months = invoice_data.months.all()
                student_data.append(
                    {
                        "student": student,
                        "registration_fee_status": registration_fee_status,
                        "invoice_data": invoice_data,
                        "shifts": shifts,
                        "months": months,
                        "location": location,
                        "role": "librarian",
                    }
                )

        return render(
            request,
            "marketing_page.html",
            {"student_data": student_data, "role": "librarian", "location": location},
        )
    else:
        # messages.error(request, "You are not authorized to view this page.")
        return render(request, "unauthorized.html", {"role": "librarian"})


@login_required(login_url="/librarian/login/")
@membership_required("Standard")
def process_student_checkbox(request):
    user = request.user
    if request.method == "POST":
        student_slugs = request.POST.getlist("student_checkbox")
        request.session["selected_students"] = student_slugs
        return redirect("marketing_send_notifications")

    return redirect("marketing-data")


def marketing_send_notifications(request):
    selected_students = request.session.get("selected_students", [])
    students = StudentData.objects.filter(slug__in=selected_students)
    return render(
        request,
        "marketing_notifications.html",
        {"selected_students": students, "role": "librarian", "template_contents":template_content},
    )


def send_notifications(request):
    if request.method != "POST":
        return redirect("marketing_send_notifications")

    student_ids = request.POST.getlist("student_ids[]")
    email_template = request.POST.get("email_template")
    email_subject = request.POST.get("email_subject")
    email_content = request.POST.get("email_content")
    sms_template = request.POST.get("sms_template")
    # sms_content = request.POST.get("sms_content")

    students = StudentData.objects.filter(id__in=student_ids)
    # Send emails
    if email_subject and email_content:
        for student in students:
            try:
                # Replace placeholders with student data
                personalized_content = email_content.replace(
                    "{student_name}", student.name
                )

                # Prepare email context
                email_context = {"content": personalized_content}

                # Use the dynamic email function
                send_dynamic_email(
                    subject=email_subject,
                    template_name="base_template.html",
                    context=email_context,
                    to_email=student.email,
                )

            except Exception as e:
                messages.error(
                    request, f"Failed to send email to {student.email}: {str(e)}"
                )
    # Send SMS
    if  sms_template:
        template_data = template_content[sms_template]
        sms_content =  template_data["content"]
        template_id = template_data['template_id']
        for student in students:
            try:
                # Replace placeholders with student data
                personalized_sms = sms_content.replace("{#var#}", student.name)
                sms_response = send_bulk_sms(student.mobile, personalized_sms, template_id)
                print(sms_response)
            except Exception as e:
                messages.error(
                    request, f"Failed to send SMS to {student.mobile}: {str(e)}"
                )
    else:
        messages.success(request, "All fields are required")
        return redirect("send_notifications")
    
    messages.success(request, "Notifications sent successfully!")
    return redirect("marketing-data")


def send_student_email(student):
    try:
        student = StudentData.objects.get(slug=student)
        subject = "Important Message from Library"
        message = f"Dear {student.name},\n\nWe have an important update for you regarding your library account.\n\nBest Regards,\nThe Library Team"
        from_email = settings.EMAIL_HOST_USER or settings.DEFAULT_FROM_EMAIL
        recipient_list = [student.email]

        send_mail(subject, message, from_email, recipient_list)
    except StudentData.DoesNotExist:
        pass


def get_int_value(post_data, key, default=0):
    value = post_data.get(key, "")
    return int(value) if value else default


@login_required(login_url="/librarian/login/")
@membership_required("Premium")
def daily_transactions(request, *args, **kwargs):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    location = librarian.librarian_address

    if user.is_authenticated and librarian.is_librarian:
        if request.method == "POST":
            try:
                date = datetime.now().date()
                opening_balance = get_int_value(request.POST, "opening_balance")
                other_income_cash = get_int_value(request.POST, "other_income_cash")
                deposit = get_int_value(request.POST, "deposit")
                sales_return_cash = get_int_value(request.POST, "sales_return_cash")
                other_expenses_cash = get_int_value(request.POST, "other_expenses_cash")

                yesterday_date = datetime.now().date() - timedelta(days=1)
                try:
                    yesterday_transaction = DailyTransaction.objects.get(
                        date=yesterday_date
                    )
                except DailyTransaction.DoesNotExist:
                    yesterday_transaction = DailyTransaction.objects.filter(
                        librarian=librarian
                    ).last()

                if opening_balance:
                    opening_balance = opening_balance
                else:
                    opening_balance = yesterday_transaction.closing_balance_cash  # type: ignore

                cash_payment = 0
                online_payment = 0
                total_student_fees_collection = 0
                total_invoice_collection = 0

                invoices_data = Invoice.objects.filter(
                    issue_date=datetime.now().date(), student__librarian=librarian
                )

                for invoice in invoices_data:
                    if invoice.mode_pay == "cash":
                        cash_payment += invoice.total_amount
                    else:
                        online_payment += invoice.total_amount

                    try:
                        registration_fee = RegistrationFee.objects.get(
                            student=invoice.student
                        )
                        if (
                            registration_fee.is_paid
                            and registration_fee.student.registration_date == date
                        ):

                            total_student_fees_collection += (
                                registration_fee.student.registration_fee
                            )

                    except RegistrationFee.DoesNotExist:
                        pass

                    total_invoice_collection += (
                        invoice.total_amount - total_student_fees_collection
                    )

                sales_cash = cash_payment
                sales_online = online_payment
                other_income_online = 0
                sales_return_online = 0
                other_expenses_online = 0
                transaction_count = invoices_data.count()
                total_daily_collection = (int(sales_cash) + int(sales_online)) + (
                    int(other_income_cash) + int(other_income_online)
                )

                closing_balance_online = (
                    opening_balance
                    + sales_cash
                    + sales_online
                    + other_income_cash
                    + other_income_online
                    - deposit
                    - sales_return_cash
                    - sales_return_online
                    - other_expenses_cash
                    - other_expenses_online
                )
                closing_balance_cash = (
                    opening_balance
                    + sales_cash
                    + other_income_cash
                    - deposit
                    - sales_return_cash
                    - other_expenses_cash
                )

                transactions = DailyTransaction.objects.create(
                    librarian=librarian,
                    date=date,
                    opening_balance=opening_balance,
                    closing_balance_online=closing_balance_online,
                    closing_balance_cash=closing_balance_cash,
                    sales_cash=sales_cash,
                    sales_online=sales_online,
                    deposit=deposit,
                    other_income_cash=other_income_cash,
                    other_income_online=other_income_online,
                    sales_return_cash=sales_return_cash,
                    sales_return_online=sales_return_online,
                    other_expenses_cash=other_expenses_cash,
                    other_expenses_online=other_expenses_online,
                    transaction_count=transaction_count,
                    total_student_fees_collection=total_student_fees_collection,
                    total_invoice_collection=total_invoice_collection,
                    total_daily_collection=total_daily_collection,
                )
                
                messages.success(
                    request,
                    f"Daily transaction for {date} has been created successfully.",
                )

                return redirect("/librarian/daily-transaction/")
            except Exception as e:
                messages.error(request, f"An error occurred: {str(e)}")
        try:
            # Retrieve all transactions for the given librarian, sort them in reverse order, and get the first one
            last_transaction = DailyTransaction.objects.filter(librarian=librarian)[
                ::-1
            ][0]
        except IndexError:
            # Handle the case where no transactions are found
            last_transaction = None
        except ObjectDoesNotExist:
            # Handle any potential ObjectDoesNotExist exceptions
            last_transaction = None
        return render(
            request,
            "daily_transactions.html",
            {
                "role": "librarian",
                "location": location,
                "transactions": last_transaction,
            },
        )
    else:
        # messages.error(request, "You are not authorized to view this page.")
        return render(request, "unauthorized.html", {"role": "librarian"})

from dateutil.relativedelta import relativedelta
def generate_transaction_report(request, *args, **kwargs):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    location = librarian.librarian_address

    if user.is_authenticated and librarian.is_librarian:
        start= datetime.now()  # Example start date
        three_months_ago = start - relativedelta(months=3)
        if request.method == "POST":
            # Check if the request is for generating the report
            if "generate_report" in request.POST:
                try:
                    # Get date range from POST request
                    start_date_str = request.POST.get("start_date")
                    end_date_str = request.POST.get("end_date")
                    start_date = (
                        datetime.strptime(start_date_str, "%Y-%m-%d").date()
                        if start_date_str
                        else datetime.now().date()
                    )
                    end_date = (
                        datetime.strptime(end_date_str, "%Y-%m-%d").date()
                        if end_date_str
                        else start_date
                    )

                    # Get daily transaction data for the given date range
                    transactions = DailyTransaction.objects.filter(
                        librarian=librarian, date__range=[start_date, end_date]
                    )

                    transactions_by_date = {transaction.date: transaction for transaction in transactions}

             
                    # Initialize calculation variables
                    cash_payment = 0
                    online_payment = 0
                    total_student_fees_collection = 0
                    total_invoice_collection = 0

                    invoices_data = Invoice.objects.filter(
                        issue_date__range=[start_date, end_date],
                        student__librarian=librarian,
                    )

                    for invoice in invoices_data:
                        if invoice.mode_pay == "cash":
                            cash_payment += invoice.total_amount
                        else:
                            online_payment += invoice.total_amount

                        try:
                            registration_fee = RegistrationFee.objects.get(
                                student=invoice.student
                            )
                            if (
                                registration_fee.is_paid
                                and registration_fee.student.registration_date
                                == start_date
                            ):
                                total_student_fees_collection += (
                                    registration_fee.student.registration_fee
                                )
                        except RegistrationFee.DoesNotExist:
                            pass

                        total_invoice_collection += (
                            invoice.total_amount - total_student_fees_collection
                        )

                    # Calculate totals and closing balances for each transaction
                    total_cash = cash_payment
                    total_online = online_payment
                    total_daily_collection = total_cash + total_online

                    closing_balance_online = (
                        total_cash
                        + total_online
                        + total_student_fees_collection
                        - total_invoice_collection
                    )
                    closing_balance_cash = (
                        total_cash
                        + total_student_fees_collection
                        - total_invoice_collection
                    )
                    date_range = [start_date + timedelta(days=x) for x in range((end_date - start_date).days + 1)]
                    # Render the data to the template
                    transactions_list = []
                    for date in date_range:
                        if date in transactions_by_date:
                            transactions_list.append(transactions_by_date[date])
                        else:
                            transactions_list.append(
                                DailyTransaction(
                                    librarian=librarian,
                                    date=date,
                                    opening_balance=0,
                                    closing_balance_online=0,
                                    closing_balance_cash=0,
                                    sales_cash=0,
                                    sales_online=0,
                                    deposit=0,
                                    other_income_cash=0,
                                    other_income_online=0,
                                    sales_return_cash=0,
                                    sales_return_online=0,
                                    other_expenses_cash=0,
                                    other_expenses_online=0,
                                    transaction_count=0,
                                    total_student_fees_collection=0,
                                    total_invoice_collection=0,
                                    total_daily_collection=0,
                                )
                            )
                    
                    return render(
                        request,
                        "transaction_report.html",
                        {
                            "role": "librarian",
                            "location": location,
                            "transactions": transactions_list,
                            "three_months_ago": three_months_ago,
                            "start_date": start_date,
                            "end_date": end_date,
                            "total_cash": total_cash,
                            "total_online": total_online,
                            "total_daily_collection": total_daily_collection,
                            "closing_balance_online": closing_balance_online,
                            "closing_balance_cash": closing_balance_cash,
                            "generated_report": True,
                        },
                    )
                except Exception as e:
                    messages.error(request, f"An error occurred: {str(e)}")
            else:
                # Handle other POST requests (e.g., form submissions) here
                pass
        else:
            # Handle the initial page load (no POST request)
            date = datetime.now().date()
            transactions = DailyTransaction.objects.filter(
                librarian=librarian, date=date
            )
            
            
            return render(
                request,
                "transaction_report.html",
                {
                    "role": "librarian",
                    "location": location,
                    "transactions": transactions,
                    "three_months_ago": three_months_ago,
                },
            )
    else:
        return render(request, "unauthorized.html", {"role": "librarian"})


@login_required(login_url="/librarian/login/")
@membership_required("Advanced")
def library_analytical_data(request, *args, **kwargs):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    location = librarian.librarian_address

    if user.is_authenticated and librarian.is_librarian:
        # Calculate all analytics data using the new function
        analytics_data = calculate_dashboard_analytics(librarian)

        # students related data
        student_data = StudentData.objects.filter(librarian=librarian)
        total_student_data = student_data.count()
        male_student_data = student_data.filter(gender="male").count()
        female_student_data = student_data.filter(gender="female").count()

        current_date = timezone.now().date()
        seven_days_from_now = current_date + timedelta(days=7)

        # Query for invoices due within the next 7 days
        invoices_student_in_seven_days = Invoice.objects.filter(
            student__librarian=librarian,
            due_date__gte=current_date,
            due_date__lte=seven_days_from_now
        )

        total_amount_due = invoices_student_in_seven_days.aggregate(
            total=Sum("total_amount")
        )["total"]

        if total_amount_due is None:
            total_amount_due = 0

        # Get monthly student counts if available
        current_month = timezone.now().replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )
        current_month_registrations = student_data.filter(
            registration_date__year=current_month.year,
            registration_date__month=current_month.month,
        ).count()

        # visitors related data
        visitors_data = Visitor.objects.filter(librarian=librarian)
        total_visitors_data = visitors_data.count()
        pending_visitors_data = visitors_data.filter(status="pending").count()
        completed_visitors_data = visitors_data.filter(status="completed").count()
        cancelled_visitors_data = visitors_data.filter(status="cancelled").count()

        # financial related data
        daily_transactions_data = DailyTransaction.objects.filter(librarian=librarian)

        yesterday_closing_balance = 0
        daily_total_balance = 0

        if daily_transactions_data.exists():
            yesterday_closing_balance = daily_transactions_data.last().closing_balance_online  # type: ignore
            daily_total_balance = daily_transactions_data.last().total_daily_collection  # type: ignore

        context = {
            "student_data": student_data,
            "total_student_data": total_student_data,
            "male_student_data": male_student_data,
            "female_student_data": female_student_data,
            "monthly_counts": current_month_registrations,
            "students_due_in_seven_days": invoices_student_in_seven_days.count(),
            "total_amount_due": total_amount_due,
            "month": current_month,
            "visitors_data": visitors_data,
            "total_visitors_data": total_visitors_data,
            "pending_visitors_data": pending_visitors_data,
            "completed_visitors_data": completed_visitors_data,
            "cancelled_visitors_data": cancelled_visitors_data,
            "yesterday_closing_balance": yesterday_closing_balance,
            "daily_total_balance": daily_total_balance,
            "role": "librarian",
            "location": location,
            # New analytics data
            "students_this_month": analytics_data['students_this_month'],
            "students_growth_percent": analytics_data['students_growth_percent'],
            "students_growth_positive": analytics_data['students_growth_positive'],
            "total_students_growth_percent": analytics_data['total_students_growth_percent'],
            "total_students_growth_positive": analytics_data['total_students_growth_positive'],
            "male_growth_percent": analytics_data['male_growth_percent'],
            "male_growth_positive": analytics_data['male_growth_positive'],
            "female_growth_percent": analytics_data['female_growth_percent'],
            "female_growth_positive": analytics_data['female_growth_positive'],
            "new_registrations_this_month": analytics_data['new_registrations_this_month'],
            "registrations_growth_percent": analytics_data['registrations_growth_percent'],
            "registrations_growth_positive": analytics_data['registrations_growth_positive'],
            "todays_collection": analytics_data['todays_collection'],
            "student_growth_months": analytics_data['growth_months'],
            "student_growth_counts": analytics_data['growth_counts'],
            "revenue_months": analytics_data['revenue_months'],
            "revenue_amounts": analytics_data['revenue_amounts'],
            "visitor_days": analytics_data['visitor_days'],
            "visitor_counts": analytics_data['visitor_counts'],
        }
        return render(request, "analytics.html", context)
    else:
        # messages.error(request, "You are not authorized to view this page.")
        return render(request, "unauthorized.html", {"role": "librarian"})


def calculate_dashboard_analytics(librarian):
    """
    Smart analytics calculation with caching.
    Only recalculates if there's new activity or cache is stale.
    Returns a dictionary with all calculated metrics.
    """
    from .models import AnalyticsCache

    # Get or create analytics cache
    cache, created = AnalyticsCache.objects.get_or_create(librarian=librarian)

    # Force recalculation if cache doesn't have new fields or they're None
    if not created:
        try:
            # Check if new fields exist and have valid values
            _ = cache.total_students_growth_percent
            _ = cache.male_growth_percent
            _ = cache.female_growth_percent
        except AttributeError:
            # New fields don't exist, force recalculation
            cache.force_recalculate = True

    # Check if we need to recalculation
    if not created and not cache.needs_recalculation():
        # Return cached data
        return {
            'students_this_month': cache.students_this_month,
            'students_growth_percent': cache.students_growth_percent,
            'students_growth_positive': cache.students_growth_positive,
            'total_students_growth_percent': cache.total_students_growth_percent,
            'total_students_growth_positive': cache.total_students_growth_positive,
            'male_growth_percent': cache.male_growth_percent,
            'male_growth_positive': cache.male_growth_positive,
            'female_growth_percent': cache.female_growth_percent,
            'female_growth_positive': cache.female_growth_positive,
            'new_registrations_this_month': cache.new_registrations_this_month,
            'registrations_growth_percent': cache.registrations_growth_percent,
            'registrations_growth_positive': cache.registrations_growth_positive,
            'todays_collection': float(cache.todays_collection),
            'growth_months': cache.get_growth_months(),
            'growth_counts': cache.get_growth_counts(),
            'revenue_months': cache.get_revenue_months(),
            'revenue_amounts': cache.get_revenue_amounts(),
            'visitor_days': cache.get_visitor_days(),
            'visitor_counts': cache.get_visitor_counts(),
        }

    # Recalculate analytics data
    current_date = timezone.now().date()
    current_month_start = timezone.now().replace(day=1).date()
    last_month_start = (current_month_start - relativedelta(months=1))
    last_month_end = current_month_start - timedelta(days=1)

    # Get all students for this librarian
    all_students = StudentData.objects.filter(librarian=librarian)

    # 1. Students This Month (who paid fees this month)
    # This includes new admissions + existing students who paid
    current_month_invoices = Invoice.objects.filter(
        student__librarian=librarian,
        issue_date__gte=current_month_start,
        is_active=True
    )
    students_this_month = current_month_invoices.values('student').distinct().count()

    # Last month's students who paid for comparison
    last_month_invoices = Invoice.objects.filter(
        student__librarian=librarian,
        issue_date__gte=last_month_start,
        issue_date__lte=last_month_end,
        is_active=True
    )
    students_last_month = last_month_invoices.values('student').distinct().count()

    # Calculate percentage growth for students this month
    if students_last_month > 0:
        students_growth_percent = round(((students_this_month - students_last_month) / students_last_month) * 100, 1)
    else:
        students_growth_percent = 100 if students_this_month > 0 else 0

    # Total Students percentage calculation (total till date comparison)
    total_students_current = all_students.count()
    total_students_last_month = all_students.filter(
        registration_date__lt=current_month_start
    ).count()

    if total_students_last_month > 0:
        total_students_growth_percent = round(((total_students_current - total_students_last_month) / total_students_last_month) * 100, 1)
    else:
        total_students_growth_percent = 100 if total_students_current > 0 else 0

    # Male/Female student percentage calculations
    # Current month male registrations
    male_students_this_month = all_students.filter(
        registration_date__gte=current_month_start,
        gender="male"
    ).count()

    # Last month male registrations
    male_students_last_month = all_students.filter(
        registration_date__gte=last_month_start,
        registration_date__lte=last_month_end,
        gender="male"
    ).count()

    if male_students_last_month > 0:
        male_growth_percent = round(((male_students_this_month - male_students_last_month) / male_students_last_month) * 100, 1)
    else:
        male_growth_percent = 100 if male_students_this_month > 0 else 0

    # Current month female registrations
    female_students_this_month = all_students.filter(
        registration_date__gte=current_month_start,
        gender="female"
    ).count()

    # Last month female registrations
    female_students_last_month = all_students.filter(
        registration_date__gte=last_month_start,
        registration_date__lte=last_month_end,
        gender="female"
    ).count()

    if female_students_last_month > 0:
        female_growth_percent = round(((female_students_this_month - female_students_last_month) / female_students_last_month) * 100, 1)
    else:
        female_growth_percent = 100 if female_students_this_month > 0 else 0

    # 2. New Registrations This Month
    new_registrations_this_month = all_students.filter(
        registration_date__gte=current_month_start
    ).count()

    new_registrations_last_month = all_students.filter(
        registration_date__gte=last_month_start,
        registration_date__lte=last_month_end
    ).count()

    # Calculate percentage growth for new registrations
    if new_registrations_last_month > 0:
        registrations_growth_percent = round(((new_registrations_this_month - new_registrations_last_month) / new_registrations_last_month) * 100, 1)
    else:
        registrations_growth_percent = 100 if new_registrations_this_month > 0 else 0

    # 3. Today's Collection Expected (invoices due within next 7 days)
    seven_days_from_now = current_date + timedelta(days=7)
    todays_collection = Invoice.objects.filter(
        student__librarian=librarian,
        due_date__gte=current_date,
        due_date__lte=seven_days_from_now,
        is_active=True
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    # 4. Student Growth Chart Data (last 6 months)
    six_months_ago = current_month_start - relativedelta(months=6)
    student_growth_data = (
        all_students.filter(registration_date__gte=six_months_ago)
        .annotate(month=TruncMonth('registration_date'))
        .values('month')
        .annotate(count=Count('id'))
        .order_by('month')
    )

    growth_months = [entry['month'].strftime('%B %Y') for entry in student_growth_data]
    growth_counts = [entry['count'] for entry in student_growth_data]

    # 5. Monthly Revenue (last 6 months)
    revenue_data = (
        Invoice.objects.filter(
            student__librarian=librarian,
            issue_date__gte=six_months_ago,
            is_active=True
        )
        .annotate(month=TruncMonth('issue_date'))
        .values('month')
        .annotate(total_revenue=Sum('total_amount'))
        .order_by('month')
    )

    revenue_months = [entry['month'].strftime('%B %Y') for entry in revenue_data]
    revenue_amounts = [entry['total_revenue'] for entry in revenue_data]

    # 6. Visitor Analytics (last 30 days)
    thirty_days_ago = current_date - timedelta(days=30)
    try:
        # Get visitor data with simpler aggregation to avoid SQLite issues
        visitors = Visitor.objects.filter(
            librarian=librarian,
            date__gte=thirty_days_ago
        ).values_list('date', flat=True)

        # Group by date manually
        visitor_counts_dict = {}
        for visit_date in visitors:
            date_key = visit_date.strftime('%m/%d')
            visitor_counts_dict[date_key] = visitor_counts_dict.get(date_key, 0) + 1

        # Sort by date and prepare data
        sorted_dates = sorted(visitor_counts_dict.keys())
        visitor_days = sorted_dates[-10:] if len(sorted_dates) > 10 else sorted_dates  # Last 10 days
        visitor_counts = [visitor_counts_dict[day] for day in visitor_days]

    except Exception as e:
        # Fallback data if visitor analytics fails
        visitor_days = []
        visitor_counts = []

    # Prepare return data
    analytics_data = {
        'students_this_month': students_this_month,
        'students_growth_percent': students_growth_percent,
        'students_growth_positive': students_growth_percent >= 0,
        'total_students_growth_percent': total_students_growth_percent,
        'total_students_growth_positive': total_students_growth_percent >= 0,
        'male_growth_percent': male_growth_percent,
        'male_growth_positive': male_growth_percent >= 0,
        'female_growth_percent': female_growth_percent,
        'female_growth_positive': female_growth_percent >= 0,
        'new_registrations_this_month': new_registrations_this_month,
        'registrations_growth_percent': registrations_growth_percent,
        'registrations_growth_positive': registrations_growth_percent >= 0,
        'todays_collection': todays_collection,
        'growth_months': growth_months,
        'growth_counts': growth_counts,
        'revenue_months': revenue_months,
        'revenue_amounts': revenue_amounts,
        'visitor_days': visitor_days,
        'visitor_counts': visitor_counts,
    }

    # Update cache with new data
    cache.students_this_month = students_this_month
    cache.students_growth_percent = students_growth_percent
    cache.students_growth_positive = students_growth_percent >= 0
    cache.total_students_growth_percent = total_students_growth_percent
    cache.total_students_growth_positive = total_students_growth_percent >= 0
    cache.male_growth_percent = male_growth_percent
    cache.male_growth_positive = male_growth_percent >= 0
    cache.female_growth_percent = female_growth_percent
    cache.female_growth_positive = female_growth_percent >= 0
    cache.new_registrations_this_month = new_registrations_this_month
    cache.registrations_growth_percent = registrations_growth_percent
    cache.registrations_growth_positive = registrations_growth_percent >= 0
    cache.todays_collection = todays_collection

    # Update chart data
    cache.set_growth_months(growth_months)
    cache.set_growth_counts(growth_counts)
    cache.set_revenue_months(revenue_months)
    cache.set_revenue_amounts(revenue_amounts)
    cache.set_visitor_days(visitor_days)
    cache.set_visitor_counts(visitor_counts)

    # Reset force recalculation flag and update timestamp
    cache.force_recalculate = False
    cache.save()

    return analytics_data


@login_required(login_url="/librarian/login/")
@membership_required("Basic")
def library_dashboard(request, *args, **kwargs):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    location = librarian.librarian_address

    if user.is_authenticated and librarian.is_librarian:
        # Calculate all analytics data using the new function
        analytics_data = calculate_dashboard_analytics(librarian)

        # Get the current librarian's associated library
        students_data = StudentData.objects.filter(librarian__user=user)

        # Fetch student data for the current month (for backward compatibility)
        current_month_start = now().replace(day=1)
        current_month_end = now()

        student_data = StudentData.objects.filter(
            librarian__user=request.user,
            registration_date__range=[current_month_start, current_month_end],
        )

        student_count = student_data.count()

        # Fetch library address
        library_address = librarian.librarian_address

        # Use new analytics data
        registration_count = analytics_data['new_registrations_this_month']

        # Calculate total invoice amount for the current month
        total_invoice_amount = (
            Invoice.objects.filter(
                student__in=students_data, issue_date__gte=current_month_start
            ).aggregate(total_amount=Sum("total_amount"))["total_amount"]
            or 0
        )

        # Count complaints per library
        complaints_count = ComplaintTicket.objects.filter(
            librarian=librarian
        ).count()

        # Prepare data for charts
        # Get the first day of the current month
        start_date = now().replace(day=1) - timedelta(days=1)
        # Get the first day of the month 6 months ago
        start_date = start_date - timedelta(days=5 * 30)  # Approximation of 6 months

        sales_data = (
            Invoice.objects.filter(
                student__in=students_data, issue_date__gte=start_date
            )
            .annotate(month=TruncMonth("issue_date"))
            .values("month")
            .annotate(total_sales=Sum("total_amount"))
            .order_by("month")
        )

        sales_months = [entry["month"].strftime("%B %Y") for entry in sales_data]
        sales_amounts = [entry["total_sales"] for entry in sales_data]

        user_growth_data = (
            students_data.annotate(month=TruncMonth("registration_date"))
            .values("month")
            .annotate(user_count=Count("id"))
            .filter(month__gte=start_date)
            .order_by("month")
        )

        growth_months = [entry["month"].strftime("%B %Y") for entry in user_growth_data]
        user_counts = [entry["user_count"] for entry in user_growth_data]

        # State-wise student count with state names
        state_wise_student_count = (
            students_data.values("locality")
            .annotate(count=Count("id"))
            .order_by("state__name")
        )

        expire_seat_count = update_seat_availability(librarian)

        return render(
            request,
            "dashboard.html",
            {
                "role": "librarian",
                "students_data": student_data,
                "students_count": student_count,
                "library_address": library_address,
                "registration_count": registration_count,
                "total_invoice_amount": total_invoice_amount,
                "complaints_count": complaints_count,
                "sales_months": sales_months,
                "sales_amounts": sales_amounts,
                "growth_months": growth_months,
                "user_counts": user_counts,
                "state_wise_student_count": state_wise_student_count,
                "location": location,
                "lib": librarian,
                "expire_seat_count": expire_seat_count,
                # New analytics data
                "students_this_month": analytics_data['students_this_month'],
                "students_growth_percent": analytics_data['students_growth_percent'],
                "students_growth_positive": analytics_data['students_growth_positive'],
                "new_registrations_this_month": analytics_data['new_registrations_this_month'],
                "registrations_growth_percent": analytics_data['registrations_growth_percent'],
                "registrations_growth_positive": analytics_data['registrations_growth_positive'],
                "todays_collection": analytics_data['todays_collection'],
                "student_growth_months": analytics_data['growth_months'],
                "student_growth_counts": analytics_data['growth_counts'],
                "revenue_months": analytics_data['revenue_months'],
                "revenue_amounts": analytics_data['revenue_amounts'],
                "visitor_days": analytics_data['visitor_days'],
                "visitor_counts": analytics_data['visitor_counts'],
            },
        )
    elif user.is_authenticated:
        return redirect("/membership/plans/")
    else:
        # messages.error(request, "You are not authorized to view this page.")
        return render(request, "unauthorized.html", {"role": "librarian"})


# Shift-Data

# def shifts_create(request):
#     user = request.user
#     librarian = Librarian_param.objects.get(user=user)
#     location = librarian.librarian_address

#     if user.is_authenticated and librarian.is_librarian:
#         if request.method == "POST":
#             name = request.POST.get("name")
#             time_range = request.POST.get("time")
#             price = request.POST.get("price")

#             Shift.objects.create(
#                 librarian=librarian,
#                 name=name,
#                 time_range=time_range,
#                 price=price,
#             )

#             messages.success(request, "Shift created successfully")
#             return redirect("/librarian/shifts")
#         shifts = Shift.objects.filter(librarian=librarian)
#         # plan = Membership.objects.get(librarian=librarian).plan
#         # required_plan_level = get_plan_level(plan)


#         return render(
#             request,
#             "shifts_list.html",
#             {"shifts": shifts, "role": "librarian", "location": location},
#         )
#     else:
#         # messages.error(request, "You are not authorized to view this page.")
#         return render(request, "unauthorized.html", {"role": "librarian"})
@login_required(login_url="/librarian/login/")
@membership_required("Basic")
def shifts_create(request):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    location = librarian.librarian_address

    if user.is_authenticated and librarian.is_librarian:
        if request.method == "POST":
            name = request.POST.get("name")
            time_range = request.POST.get("time")
            price = request.POST.get("price")

            # Create the new shift
            new_shift = Shift.objects.create(
                librarian=librarian,
                name=name,
                time_range=time_range,
                price=price,
            )

            # Fetch existing shifts for the librarian
            existing_shifts = Shift.objects.filter(librarian=librarian).exclude(
                id=new_shift.id
            )

            # If there are existing shifts, check for seats to duplicate
            if existing_shifts.exists():
                # Find any seats from the existing shifts
                existing_seats = Seat.objects.filter(shift__in=existing_shifts)

                if existing_seats.exists():
                    # Duplicate the seats from the existing shifts to the new shift
                    for seat in existing_seats:
                        # Check if a seat with the same number already exists for the new shift
                        if not Seat.objects.filter(
                            seat_number=seat.seat_number,
                            librarian=librarian,
                            shift=new_shift,
                        ).exists():
                            # Create a new seat only if it does not already exist
                            Seat.objects.create(
                                seat_number=seat.seat_number,
                                librarian=librarian,
                                shift=new_shift,
                            )

            messages.success(request, "Shift created successfully")
            return redirect("/librarian/shifts")

        # Fetch all shifts for display
        shifts = Shift.objects.filter(librarian=librarian)

        return render(
            request,
            "shifts_list.html",
            {"shifts": shifts, "role": "librarian", "location": location},
        )
    else:
        # If user is not authorized
        return render(request, "unauthorized.html", {"role": "librarian"})


# @login_required(login_url="/librarian/login/")
# @membership_required("Basic")
def shifts_update(request, pk):
    shift = get_object_or_404(Shift, pk=pk)
    if request.method == "POST":
        # Get the updated values
        new_name = request.POST.get("name")
        new_time_range = request.POST.get("time")
        new_price = request.POST.get("price")

        # Save the old values for comparison (if needed)
        old_name = shift.name

        # Update the shift object
        shift.name = new_name
        shift.time_range = new_time_range
        shift.price = new_price
        shift.save()

        # Update related seats if needed (e.g., if seat naming depends on shift name)
        if new_name != old_name:
            related_seats = Seat.objects.filter(shift=shift)
            for seat in related_seats:
                # Example: Updating seat names with the new shift name
                seat.seat_number = seat.seat_number.replace(old_name, new_name)
                seat.save()

        messages.success(
            request, "Shift updated successfully, and related seats were updated."
        )
        if Sublibrarian_param.objects.filter(user =request.user).exists():
            return redirect('shifts_create_sub')
        return redirect("/librarian/shifts")
    role = 'librarian'
    if Sublibrarian_param.objects.filter(user =request.user).exists():
        role= "sublibrarian"
    return render(request, "shifts_list.html", {"shift": shift, "role": role})


# @login_required(login_url="/librarian/login/")
# @membership_required("Basic")
def shifts_delete(request, pk):
    shift = get_object_or_404(Shift, pk=pk)
    shift.delete()
    messages.info(request, "Shift deleted successfully")
    if Sublibrarian_param.objects.filter(user =request.user).exists():
        return redirect('shifts_create_sub')
    return redirect("/librarian/shifts")


def upgrade_plan(request):
    librarian = Librarian_param.objects.get(user=request.user)
    membership = Membership.objects.get(librarian=librarian)

    return render(request, "upgrade_plan.html", {"membership": membership})


@login_required(login_url="/librarian/login/")
@membership_required("Basic")
def create_ticket(request):
    user = request.user
    if user.is_authenticated:
        if request.method == "POST":
            title = request.POST.get("title")
            description = request.POST.get("description")
            if title and description:
                ticket = ComplaintTicket(
                    user=request.user, title=title, description=description
                )
                ticket.save()
                return redirect("ticket_list")
        return render(request, "create_ticket.html")
        return render(request, "create_ticket.html")
    else:
        # messages.error(request, "You are not authorized to view this page.")
        return render(request, "unauthorized.html", {"role": "librarian"})


def help_page(request):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    if user.is_authenticated and librarian.is_librarian:
        if request.method == "POST":
            issue_type = request.POST.get("issueType")
            title = request.POST.get("title")
            description = request.POST.get("description")

            try:
                librarian = Librarian_param.objects.get(user=request.user)

                if issue_type and title and description:
                    # Create a new ticket
                    ticket = ComplaintTicket(
                        librarian=librarian,
                        issue_type=issue_type,
                        subject=title,
                        description=description,
                    )
                    ticket.save()

                    # Start a new thread to send emails
                    email_thread = threading.Thread(
                        target=send_ticket_emails_thread, args=(ticket.ticket_number,)  # type: ignore
                    )
                    email_thread.start()

                    messages.success(request, "Complaint Registered Successfully")
                    return redirect("/librarian/help/")
            except Librarian_param.DoesNotExist:
                messages.error(request, "Librarian profile not found.")

        return render(request, "help.html", {"role": "librarian"})
    else:
        return render(request, "unauthorized.html", {"role": "librarian"})


def send_ticket_emails_thread(ticket_id):
    # Set up a new database connection for this thread
    connection.close()
    connection.connect()

    try:
        # Fetch the ticket object in this thread
        ticket = ComplaintTicket.objects.select_related("librarian__user").get(
            id=ticket_id
        )

        # Prepare the context for rendering the email templates
        context = {
            "ticket_number": ticket.ticket_number,
            "type": ticket.issue_type,
            "subject": ticket.subject,
            "description": ticket.description,
            "librarian": ticket.librarian.library_name,
        }

        # Render the email templates
        librarian_message_html = render_to_string(
            "librarian_ticket_email.html", context
        )
        commander_message_html = render_to_string(
            "commander_ticket_email.html", context
        )

        # Send email to librarian
        librarian_email = EmailMessage(
            subject="New Complaint Ticket Registered",
            body=librarian_message_html,
            from_email=settings.EMAIL_HOST_USER,
            to=[ticket.librarian.user.email],
        )
        librarian_email.content_subtype = "html"
        librarian_email.send()

        # Send email to library commander
        commander_email = EmailMessage(
            subject="New Complaint Ticket Registered",
            body=commander_message_html,
            from_email=settings.EMAIL_HOST_USER,
            to=["<EMAIL>"],  # Replace with actual email
        )
        commander_email.content_subtype = "html"
        commander_email.send()

    except Exception as e:
        print(f"Error sending email: {str(e)}")
    finally:
        # Close the database connection
        connection.close()


def send_feedback_email(name, email, feedback):
    subject = "Feedback Received"
    message = f"Name: {name}\nEmail: {email}\n\nFeedback:\n{feedback}"
    from_email = settings.DEFAULT_FROM_EMAIL
    recipient_list = [
        settings.DEFAULT_FROM_EMAIL
    ]  # Email address to receive the feedback

    send_mail(subject, message, from_email, recipient_list)


def feedback_page(request):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    if user.is_authenticated and librarian.is_librarian:
        if request.method == "POST":
            name = request.POST.get("name")
            email = request.POST.get("email")
            feedback = request.POST.get("feedback")

            # Create and start a thread for sending the email
            email_thread = threading.Thread(
                target=send_feedback_email, args=(name, email, feedback)
            )
            email_thread.start()
            messages.success(
                request,
                "Thank you for your feedback! We'll get back to you shortly.",
            )

            return render(
                request,
                "feedback.html",
                {"role": "librarian", "message": "Thank you for your feedback!"},
            )

        return render(request, "feedback.html", {"role": "librarian"})
    else:
        # messages.error(request, "You are not authorized to view this page.")
        return render(request, "unauthorized.html", {"role": "librarian"})


@login_required(login_url="/librarian/login/")
@membership_required("Advanced")
def sublibrarian_update(request, id):
    sublibrarian_profile = Sublibrarian_param.objects.get(id=id)

    if request.method == "POST":
        # Get data from the form for the User model
        first_name = request.POST.get("first_name")
        last_name = request.POST.get("last_name")
        email = request.POST.get("email")

        # Get data for the Sublibrarian_param model
        phone = request.POST.get("phone")
        address = request.POST.get("address")

        # Update the User model fields
        sublibrarian_profile.user.first_name = first_name
        sublibrarian_profile.user.last_name = last_name
        sublibrarian_profile.user.email = email
        sublibrarian_profile.user.save()

        # Update the Sublibrarian_param model fields
        sublibrarian_profile.sublibrarian_phone_num = phone
        sublibrarian_profile.sublibrarian_address = address
        sublibrarian_profile.save()

        messages.success(request, "Sublibrarian data updated successfully.")
        return redirect("/librarian/profile")

    return render(
        request,
        "edit_sublibrarian.html",
        {
            "sub": sublibrarian_profile,
        },
    )


@login_required(login_url="/librarian/login/")
@membership_required("Standard")
def sublibrarian_delete(request, id):
    sublibrarian_profile = Sublibrarian_param.objects.get(id=id)
    sublibrarian_profile.delete()
    sublibrarian_profile.user.delete()

    return redirect("/librarian/profile")


def library_list_display(request):
    library = Librarian_param.objects.all()
    return render(request, "library_list.html", {"library": library})


def send_email_async(subject, template_name, context, from_email, recipient_list):
    try:
        html_content = render_to_string(template_name, context)
        text_content = strip_tags(html_content)

        msg = EmailMultiAlternatives(subject, text_content, from_email, recipient_list)
        msg.attach_alternative(html_content, "text/html")
        msg.send()
    except SMTPException as e:
        print(f"Failed to send email: {str(e)}")


def library_details(request, slug):
    try:
        library = get_object_or_404(Librarian_param, slug=slug)
        shifts = Shift.objects.filter(librarian=library)
        qr_code_instance = generate_qr_code(library)

    except Exception as e:
        return render(
            request, "error.html", {"error_message": f"Library not found: {str(e)}"}
        )

    if request.method == "POST":
        name = request.POST.get("name").upper()
        email = request.POST.get("email")
        message = request.POST.get("message")

        if name and email and message:
            try:
                # Create and save ContactMessage object
                contact_message = ContactMessage.objects.create(
                    library=library, name=name, email=email, message=message
                )

                # Prepare email content
                subject = f"New contact message from {name}"
                from_email = settings.DEFAULT_FROM_EMAIL
                recipient_list = [
                    library.user.email
                ]  # Assuming library has an email field

                # Context for email template
                email_context = {
                    "name": name,
                    "email": email,
                    "message": message,
                    "library_name": library.library_name,  # Assuming library has a name field
                }

                # Send email asynchronously
                email_thread = threading.Thread(
                    target=send_email_async,
                    args=(
                        subject,
                        "new_contact_message.html",
                        email_context,
                        from_email,
                        recipient_list,
                    ),
                )
                email_thread.start()

                context = {
                    "library": library,
                    "success_message": "Your message has been sent successfully!",
                }
            except IntegrityError as e:
                context = {
                    "library": library,
                    "error_message": f"Failed to save your message: {str(e)}",
                }
            except Exception as e:
                context = {
                    "library": library,
                    "error_message": f"An unexpected error occurred: {str(e)}",
                }
        else:
            context = {
                "library": library,
                "error_message": "Please fill in all fields.",
            }

        return render(request, "library_detail.html", context)

    return render(
        request,
        "library_detail.html",
        {"library": library, "shifts": shifts, "qr_code": qr_code_instance},
    )


# @login_required(login_url="/librarian/login/")
# @membership_required("Advanced")
# def seat_list(request):
#     librarian = Librarian_param.objects.filter(user=request.user)
#     seats = Seat.objects.filter(librarian=librarian).order_by("seat_number")
#     return render(request, "yourapp/seat_list.html", {"seats": seats})


# @login_required(login_url="/librarian/login/")
# @membership_required("Basic")
def create_seat(request):
    librarian = Librarian_param.objects.get(user=request.user)
    shifts = Shift.objects.filter(librarian=librarian)

    if request.method == "POST":
        total_seats = int(request.POST.get("total_seats", 0))
        prefix = request.POST.get("prefix", "")

        # Fetch all shifts for the librarian

        for shift in shifts:
            # Check for existing seats with the same prefix
            existing_seats = Seat.objects.filter(
                seat_number__startswith=prefix, librarian=librarian, shift=shift
            )

            # Find the maximum number used in the existing seats
            existing_numbers = []
            for seat in existing_seats:
                # Extract the number part from the seat_number
                try:
                    num_part = int(
                        seat.seat_number[len(prefix) + 1 :]
                    )  # Get the number after the prefix and hyphen
                    existing_numbers.append(num_part)
                except ValueError:
                    continue  # If conversion fails, skip

            # Determine the starting number for new seats
            starting_number = max(existing_numbers, default=0) + 1

            # Ensure the starting number is at least 1
            starting_number = max(starting_number, 1)

            # Create seats with the given prefix and sequential numbers
            for i in range(starting_number, starting_number + total_seats):
                seat_number = f"{prefix}-{i}"  # Generate seat number with prefix
                Seat.objects.get_or_create(
                    seat_number=seat_number, librarian=librarian, shift=shift
                )

        return redirect("seats-list")

    seat_list = (
        Seat.objects.filter(librarian=librarian)
        .select_related("shift")
        .prefetch_related(
            Prefetch("booking_set", queryset=Booking.objects.select_related("student"))
        )
    ).order_by("shift")

    seat_data = []

    # Loop through each seat to gather booking data
    for seat in seat_list:
        # Extract prefix from seat number (e.g., "A-1" -> "A")
        prefix = seat.seat_number.split('-')[0] if '-' in seat.seat_number else seat.seat_number

        if seat.booking_set.exists():
            # Add each booking associated with the seat
            for booking in seat.booking_set.all():
                seat_data.append(
                    {
                        "id": seat.id,
                        "seat_number": seat.seat_number,
                        "prefix": prefix,
                        "shift": seat.shift,
                        "student_name": booking.student.name,
                        "booking_date": booking.booking_date,
                        "expire_date": booking.expire_date,
                    }
                )
        else:
            # Add seat with no booking information
            seat_data.append(
                {
                    "id": seat.id,
                    "seat_number": seat.seat_number,
                    "prefix": prefix,
                    "shift": seat.shift,
                    "student_name": None,
                    "booking_date": None,
                    "expire_date": None,
                }
            )

    # Pass seat_list and seat_data to the template
    return render(
        request,
        "create_seat.html",
        {
            "shifts": shifts,
            "seat_list": seat_list,
            "seat_data": seat_data,  # Pass the constructed seat data to the template
            "role": "librarian",
        },
    )


# Update an existing seat and apply updates across all related seats
@login_required(login_url="/librarian/login/")
@membership_required("Basic")
def update_seat(request, pk):
    # Get the original seat instance to know the initial seat number and librarian
    seat = get_object_or_404(Seat, pk=pk)
    original_seat_number = seat.seat_number
    original_librarian = seat.librarian

    if request.method == "POST":
        new_seat_number = request.POST.get("seat_number")
        new_librarian_id = request.POST.get("librarian")
        new_librarian = get_object_or_404(User, pk=new_librarian_id)

        # Update all seats that match the original seat number and librarian across shifts
        Seat.objects.filter(
            seat_number=original_seat_number, librarian=original_librarian
        ).update(seat_number=new_seat_number, librarian=new_librarian)

        return redirect("seat_list")

    librarians = User.objects.all()
    shifts = Shift.objects.all()
    return render(
        request,
        "yourapp/update_seat.html",
        {"seat": seat, "librarians": librarians, "shifts": shifts},
    )


# @login_required(login_url="/librarian/login/")
# @membership_required("Basic")
def cancel_seat(request, pk):
    seat = get_object_or_404(Seat, pk=pk)
    seat.is_available = True
    seat.save()
    if Sublibrarian_param.objects.filter(user =request.user).exists():
        return redirect('seats-list_sub')
    return redirect("seats-list")

# @login_required(login_url="/librarian/login/")
# @membership_required("Basic")
def delete_seat(request, pk):
    seat = get_object_or_404(Seat, pk=pk)
    seat.delete()
    if Sublibrarian_param.objects.filter(user =request.user).exists():
        return redirect('seats-list_sub')
    return redirect("seats-list")


def generate_qr_code(librarian):
    qr_code_instance = QRCode.objects.filter(librarian=librarian).first()

    if qr_code_instance:
        return qr_code_instance.qr_code

    qr_content = f"https://librainian.com/students/student-registration-temp/{librarian.slug}/"  # Production Url
    # qr_content = f"http://127.0.0.1:8000/students/student-registration-temp/{librarian.slug}/"  # Testing URL
    qr = qrcode.make(qr_content)

    buffer = BytesIO()
    qr.save(buffer, format="PNG")  # type: ignore
    qr_base64 = base64.b64encode(buffer.getvalue()).decode()

    QRCode.objects.create(librarian=librarian, qr_code=qr_base64)

    return qr_base64


def librarian_qr_view(request, slug):
    librarian = get_object_or_404(Librarian_param, slug=slug)
    qr_code = generate_qr_code(librarian)
    return render(
        request, "librarian_qr.html", {"librarian": librarian, "qr_code": qr_code}
    )


# views.py
from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.models import User
from .notification_utils import send_fcm_notification, send_notification_to_all_users

# views.py
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from .models import DeviceToken


@csrf_exempt  # Use this only for testing; consider using proper CSRF protection in production
def save_device_token(request):
    """Enhanced device token saving with comprehensive debugging - supports both JSON and form data"""
    print(f"\n🔔 === DEVICE TOKEN SAVE REQUEST ===")
    print(f"📡 Method: {request.method}")
    print(f"👤 User: {request.user.username if request.user.is_authenticated else 'Anonymous'}")
    print(f"🌐 User Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}")
    print(f"📍 Remote IP: {request.META.get('REMOTE_ADDR', 'Unknown')}")
    print(f"📋 Content Type: {request.META.get('CONTENT_TYPE', 'Unknown')}")

    if not request.user.is_authenticated:
        print(f"❌ User not authenticated")
        return JsonResponse({"status": "error", "message": "Authentication required."}, status=401)

    if request.method == "POST":
        # Handle both JSON and form data
        try:
            if request.META.get('CONTENT_TYPE', '').startswith('application/json'):
                # JSON data
                data = json.loads(request.body)
                token = data.get('token')
                device_type = data.get('device_type', 'web')
                device_name = data.get('device_name', '')
                print(f"📦 JSON data: {data}")
            else:
                # Form data
                token = request.POST.get("token")
                device_type = request.POST.get("device_type", 'web')
                device_name = request.POST.get("device_name", '')
                print(f"📦 POST data: {dict(request.POST)}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            return JsonResponse({"status": "error", "message": "Invalid JSON data."}, status=400)

        print(f"🎫 Token received: {token[:50] if token else 'None'}{'...' if token and len(token) > 50 else ''}")
        print(f"📱 Device type: {device_type}")
        print(f"📱 Device name: {device_name}")

        if token:
            try:
                # Get user agent for web devices
                user_agent = request.META.get('HTTP_USER_AGENT', '') if device_type == 'web' else ''

                # Extract device name from user agent if not provided
                if not device_name and user_agent:
                    if 'Chrome' in user_agent:
                        device_name = 'Chrome Browser'
                    elif 'Firefox' in user_agent:
                        device_name = 'Firefox Browser'
                    elif 'Safari' in user_agent:
                        device_name = 'Safari Browser'
                    elif 'Edge' in user_agent:
                        device_name = 'Edge Browser'
                    else:
                        device_name = 'Web Browser'

                    # Add mobile/desktop info
                    if 'Mobile' in user_agent:
                        device_name += ' (Mobile)'
                    else:
                        device_name += ' (Desktop)'

                # Check if the token already exists
                device_token, created = DeviceToken.objects.get_or_create(
                    user=request.user,
                    token=token,
                    defaults={
                        "device_type": device_type,
                        "device_name": device_name,
                        "user_agent": user_agent,
                        "is_active": True
                    }
                )

                # Update existing token info if not created
                if not created:
                    device_token.device_name = device_name or device_token.device_name
                    device_token.user_agent = user_agent or device_token.user_agent
                    device_token.is_active = True
                    device_token.save()

                print(f"💾 Database operation: {'Created new' if created else 'Updated existing'} token")
                print(f"🆔 Token ID: {device_token.id}")
                print(f"📊 User total tokens: {DeviceToken.objects.filter(user=request.user).count()}")

                if created:
                    print(f"✅ NEW TOKEN SAVED SUCCESSFULLY!")
                else:
                    print(f"ℹ️ Token updated in database")

                return JsonResponse({
                    "status": "success",
                    "message": f"Token {'saved' if created else 'updated'} successfully.",
                    "created": created,
                    "device_info": {
                        "device_type": device_type,
                        "device_name": device_name,
                        "total_devices": DeviceToken.objects.filter(user=request.user, is_active=True).count()
                    }
                })
            except Exception as e:
                print(f"❌ Database error: {e}")
                return JsonResponse(
                    {"status": "error", "message": f"Database error: {str(e)}"}
                )
        else:
            print(f"❌ Missing token")
            return JsonResponse(
                {"status": "error", "message": "Token is required."}
            )
    else:
        print(f"❌ Invalid method: {request.method}")
        return JsonResponse(
            {"status": "error", "message": "Only POST method allowed."}
        )


@login_required
def token_debug_page(request):
    """Debug page for FCM token generation and registration"""
    return render(request, 'token_debug.html')


def firebase_debug_page(request):
    """Comprehensive Firebase FCM debug page - no login required for testing"""
    return render(request, 'firebase_debug.html')


def domain_test_page(request):
    """Test if domain is whitelisted in Firebase Auth"""
    return render(request, 'domain_test.html')

    return JsonResponse({"status": "error", "message": "Invalid request method."})


def send_notification_view(request):
    if request.method == "POST":
        title = request.POST.get("title")
        body = request.POST.get("body")
        user_id = request.POST.get("user_id")
        send_to_all = request.POST.get("send_to_all")

        if send_to_all:
            send_notification_to_all_users(title=title, body=body)
            messages.success(request, "Notification sent to all users.")
        elif user_id:
            try:
                user = User.objects.get(id=user_id)
                send_fcm_notification(user=user, title=title, body=body)
                messages.success(request, f"Notification sent to {user.username}.")
            except User.DoesNotExist:
                messages.error(request, "User  not found.")
        else:
            messages.error(
                request, "Please select a user or choose to send to all users."
            )

        return redirect("send_notification")

    users = User.objects.all()  # Get all users for the dropdown
    return render(request, "send_notification.html", {"users": users})


def page_not_found_view(request, exception=None):
    return render(request, "404.html", {"role": "librarian"})


@csrf_exempt
def PageVisitors(request):
    if request.method == 'POST':
        data = json.loads(request.body)
        for key, value in data.items():
            if key == "/":
                key = "/home"
            PageCounter.objects.create(url=key, count=value)
        return HttpResponse("Data received successfully.")


# Register Views
@login_required(login_url="/librarian/login/")
def register_page(request, *args, **kwargs):
    """Main register page with two options: Student Register and Invoice Register"""
    user = request.user
    try:
        librarian = Librarian_param.objects.get(user=user)
        if librarian.is_librarian:
            return render(request, "register.html", {"role": "librarian"})
        else:
            return render(request, "unauthorized.html", {"role": "librarian"})
    except Librarian_param.DoesNotExist:
        return render(request, "unauthorized.html", {"role": "librarian"})


@login_required(login_url="/librarian/login/")
def student_register(request, *args, **kwargs):
    """Student register page showing all students in tabular/card format"""
    user = request.user
    try:
        librarian = Librarian_param.objects.get(user=user)
        if librarian.is_librarian:
            # Get all students for this librarian
            sublibrarians = Sublibrarian_param.objects.filter(librarian=librarian)
            sublibrarian_students = StudentData.objects.filter(sublibrarian__in=sublibrarians)
            librarian_students = StudentData.objects.filter(librarian=librarian)
            students = (sublibrarian_students | librarian_students).distinct().order_by('-registration_date')

            return render(request, "student_register.html", {
                "role": "librarian",
                "students": students
            })
        else:
            return render(request, "unauthorized.html", {"role": "librarian"})
    except Librarian_param.DoesNotExist:
        return render(request, "unauthorized.html", {"role": "librarian"})


@login_required(login_url="/librarian/login/")
def invoice_register(request, *args, **kwargs):
    """Invoice register page showing all invoices in the library"""
    user = request.user
    try:
        librarian = Librarian_param.objects.get(user=user)
        if librarian.is_librarian:
            # Get all students for this librarian
            sublibrarians = Sublibrarian_param.objects.filter(librarian=librarian)
            sublibrarian_students = StudentData.objects.filter(sublibrarian__in=sublibrarians)
            librarian_students = StudentData.objects.filter(librarian=librarian)
            all_students = (sublibrarian_students | librarian_students).distinct()

            # Get all invoices for these students
            all_invoices = Invoice.objects.filter(student__in=all_students).select_related('student').prefetch_related('shift', 'months').order_by('-issue_date')

            # Prepare invoice data with related information
            invoice_data = []
            for invoice in all_invoices:
                shifts = invoice.shift.all()
                months = invoice.months.all()

                # Determine status based on due date and payment status
                status = 'pending'
                if hasattr(invoice, 'payment_status') and invoice.payment_status == 'Confirm':
                    status = 'paid'
                elif invoice.due_date < timezone.now().date():
                    status = 'overdue'

                invoice_data.append({
                    'id': invoice.invoice_id,
                    'slug': invoice.slug,
                    'student_name': invoice.student.name,
                    'student_id': invoice.student.unique_id,
                    'student_course': invoice.student.course.name if invoice.student.course else 'N/A',
                    'issue_date': invoice.issue_date,
                    'due_date': invoice.due_date,
                    'total_amount': invoice.total_amount,
                    'discount_amount': invoice.discount_amount or 0,
                    'status': status,
                    'payment_status': getattr(invoice, 'payment_status', 'Pending'),
                    'description': invoice.description or f"Library Fee - {', '.join([month.name for month in months])}",
                    'shifts': [shift.name for shift in shifts],
                    'months': [month.name for month in months],
                    'mode_pay': getattr(invoice, 'mode_pay', 'Not specified')
                })

            return render(request, "invoice_register.html", {
                "role": "librarian",
                "all_invoices": invoice_data,
                "total_invoices": len(invoice_data)
            })
        else:
            return render(request, "unauthorized.html", {"role": "librarian"})
    except Librarian_param.DoesNotExist:
        return render(request, "unauthorized.html", {"role": "librarian"})


@login_required(login_url="/librarian/login/")
def get_student_invoices(request, student_slug):
    """API endpoint to fetch real invoice data for a student"""
    user = request.user
    try:
        librarian = Librarian_param.objects.get(user=user)
        # Get the student
        student = StudentData.objects.get(slug=student_slug)

        # Get all invoices for this student
        invoices = Invoice.objects.filter(student=student).order_by('-issue_date')

        # Prepare invoice data
        invoice_data = []
        for invoice in invoices:
            # Get shifts and months for this invoice
            shifts = invoice.shift.all()
            months = invoice.months.all()

            invoice_data.append({
                'id': invoice.invoice_id,
                'slug': invoice.slug,
                'date': invoice.issue_date.strftime('%Y-%m-%d'),
                'due_date': invoice.due_date.strftime('%Y-%m-%d'),
                'amount': float(invoice.total_amount),
                'discount_amount': float(invoice.discount_amount or 0),
                'status': 'overdue' if invoice.due_date < timezone.now().date() else 'pending',
                'description': invoice.description or f"Library Fee - {', '.join([month.name for month in months])}",
                'shifts': [shift.name for shift in shifts],
                'months': [month.name for month in months],
                'mode_pay': invoice.mode_pay or 'Not specified'
            })

        return JsonResponse({
            'success': True,
            'invoices': invoice_data,
            'student': {
                'name': student.name,
                'unique_id': student.unique_id,
                'slug': student.slug
            }
        })

    except Librarian_param.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Unauthorized'})
    except StudentData.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Student not found'})


# Duplicate function removed - using the enhanced version above


def test_notifications_page(request):
    """Test page for notification system"""
    return render(request, "test_notifications.html")


@csrf_exempt
@require_http_methods(["POST"])
def track_notification_event(request):
    """Track notification events (click, close, etc.)"""
    try:
        data = json.loads(request.body)
        event_type = data.get('event_type')
        notification_data = data.get('notification_data', {})

        # Log the event
        print(f"📊 Notification Event: {event_type}")
        print(f"   Data: {notification_data}")
        print(f"   Timestamp: {data.get('timestamp')}")

        # In a real implementation, you would:
        # 1. Update NotificationHistory with delivery status
        # 2. Track analytics
        # 3. Update user engagement metrics

        return JsonResponse({
            'success': True,
            'message': 'Event tracked successfully'
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


def demo_qr_registration(request):
    """Demo QR registration form"""
    return render(request, "demo_qr_registration.html")


def mobile_notification_test(request):
    """Mobile-specific notification test page"""
    return render(request, "mobile_notification_test.html")


def simple_mobile_test(request):
    """Simple mobile notification test without frameworks"""
    return render(request, "simple_mobile_test.html")


def full_notification_test(request):
    """Comprehensive notification system test"""
    return render(request, "full_notification_test.html")


def comprehensive_notification_test(request):
    """Comprehensive test for all notification events"""
    return render(request, "comprehensive_notification_test.html")


@csrf_exempt
@require_http_methods(["POST"])
def trigger_test_notification(request):
    """Trigger a test notification for full system testing"""
    try:
        data = json.loads(request.body)
        notification_type = data.get('type', 'test')
        notification_data = data.get('data', {})

        # Log the notification trigger
        print(f"🔔 Test Notification Triggered:")
        print(f"   Type: {notification_type}")
        print(f"   Data: {notification_data}")
        print(f"   Timestamp: {timezone.now()}")

        # Simulate different notification types
        if notification_type == 'qr_registration':
            title = "📝 New QR Registration"
            body = f"Student: {notification_data.get('name', 'Unknown')}\nCourse: {notification_data.get('course', 'Unknown')}"
        elif notification_type == 'payment':
            title = "💰 Payment Received"
            body = f"Amount: ₹{notification_data.get('amount', '0')}\nFrom: {notification_data.get('from', 'Unknown')}"
        elif notification_type == 'booking':
            title = "📚 Facility Booking"
            body = f"Facility: {notification_data.get('facility', 'Unknown')}\nTime: {notification_data.get('time', 'Unknown')}"
        elif notification_type == 'urgent':
            title = "🚨 URGENT Alert"
            body = f"Message: {notification_data.get('message', 'Urgent notification')}"
        else:
            title = "🔔 Test Notification"
            body = f"This is a test notification from the LMS system"

        # In a real implementation, this would:
        # 1. Find all devices for the user/library
        # 2. Send FCM notifications to all devices
        # 3. Track delivery status
        # 4. Store notification history

        # For demo, we'll simulate successful delivery
        response_data = {
            'success': True,
            'message': 'Test notification triggered successfully',
            'notification': {
                'title': title,
                'body': body,
                'type': notification_type,
                'timestamp': timezone.now().isoformat()
            },
            'delivery': {
                'total_devices': 3,  # Simulated
                'successful_deliveries': 3,
                'failed_deliveries': 0
            }
        }

        return JsonResponse(response_data)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def qr_registration_notification(request):
    """Handle QR registration notification"""
    try:
        data = json.loads(request.body)
        student_data = data.get('student_data', {})

        # Log the registration
        print(f"🔔 QR Registration Notification:")
        print(f"   Name: {student_data.get('name', 'Unknown')}")
        print(f"   Email: {student_data.get('email', 'Unknown')}")
        print(f"   Mobile: {student_data.get('mobile', 'Unknown')}")
        print(f"   Course: {student_data.get('course', 'Unknown')}")
        print(f"   Date: {student_data.get('registration_date', 'Unknown')}")

        # In a real implementation, you would:
        # 1. Find the librarian/admin to notify
        # 2. Send push notification via FCM
        # 3. Save notification to database
        # 4. Send email notification if needed

        return JsonResponse({
            'success': True,
            'message': 'QR registration notification received'
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required(login_url="/librarian/login/")
def partial_payments_list(request):
    """List all students with partial payments for librarian"""
    from studentsData.views import partial_payments_list as student_partial_payments_list
    return student_partial_payments_list(request)


@login_required(login_url="/librarian/login/")
def mark_invoice_complete(request, invoice_slug):
    """Mark invoice as complete for librarian"""
    from studentsData.views import mark_invoice_complete as student_mark_invoice_complete
    return student_mark_invoice_complete(request, invoice_slug)


@login_required(login_url="/librarian/login/")
def send_payment_reminder(request, invoice_slug):
    """Send payment reminder for librarian"""
    from studentsData.views import send_payment_reminder as student_send_payment_reminder
    return student_send_payment_reminder(request, invoice_slug)
