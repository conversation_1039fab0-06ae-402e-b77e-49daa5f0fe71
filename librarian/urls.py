from django.urls import path
from . import views


urlpatterns = [
    path("signup/", views.librarian_signup, name="lib_signup"),
    # path("accounts/profile/", views.google_signup_login, name="google_signup_login"),
    path("resend-otp/", views.resend_otp, name="resend_otp"),
    path("verify-otp/", views.verify_otp, name="verify-otp"),
    path("login/", views.librarian_login, name="lib_login"),
    path("logout/", views.librarian_logout, name="logout"),
    path("profile/", views.librarian_profile, name="lib_profile"),  # type: ignore
    path("edit-profile/", views.edit_librarian_profile, name="edit_profile"),  # type: ignore
    path("table/", views.library_data, name="student-data"),
    path(
        "process-student-data/",
        views.process_student_checkbox,
        name="process-student-data",
    ),
    path(
        "sublibrarian/update/<int:id>",
        views.sublibrarian_update,
        name="analytical-data",
    ),
    path(
        "sublibrarian/delete/<int:id>",
        views.sublibrarian_delete,
        name="analytical-data",
    ),
    path("analytics/", views.library_analytical_data, name="analytical-data"),
    path("marketing/", views.marketing_wise_data, name="marketing-data"),
    path(
        "marketing-send-notification/",
        views.marketing_send_notifications,
        name="marketing_send_notifications",
    ),
    path(
        "send-notifications/",
        views.send_notifications,
        name="send_notifications",
    ),
    path(
        "send-push-notification/",
        views.send_notification_view,
        name="send_notification",
    ),
    path("daily-transaction/", views.daily_transactions, name="daily-transaction"),
    path(
        "transaction-report/",
        views.generate_transaction_report,
        name="transaction-report",
    ),
    path("dashboard/", views.library_dashboard, name="library_dashboard"),
    path("shifts/", views.shifts_create, name="shifts_create"),
    path("shifts/update/<int:pk>/", views.shifts_update, name="shifts_update"),
    path("shifts/delete/<int:pk>/", views.shifts_delete, name="shifts_delete"),
    path("upgrade_plan", views.upgrade_plan, name="upgrade_plan"),
    path("library-list/", views.library_list_display, name="library-list"),
    path("library-details/<slug:slug>/", views.library_details, name="library-details"),
    path("seats/", views.create_seat, name="seats-list"),
    path("update-seat/<int:pk>", views.update_seat, name="update_seat"),
    path("cancel-seat/<int:pk>", views.cancel_seat, name="cancel_seat"),
    path("delete-seat/<int:pk>", views.delete_seat, name="delete_seat"),
    path("<slug:slug>/qr/", views.librarian_qr_view, name="librarian_qr"),
    path("help/", views.help_page, name="help_page"),
    path("save-device-token/", views.save_device_token, name="save_device_token"),
    path("test-notifications/", views.test_notifications_page, name="test_notifications"),
    path("demo-qr-registration/", views.demo_qr_registration, name="demo_qr_registration"),
    path("mobile-notification-test/", views.mobile_notification_test, name="mobile_notification_test"),
    path("simple-mobile-test/", views.simple_mobile_test, name="simple_mobile_test"),
    path("full-notification-test/", views.full_notification_test, name="full_notification_test"),
    path("comprehensive-notification-test/", views.comprehensive_notification_test, name="comprehensive_notification_test"),
    path("trigger-test-notification/", views.trigger_test_notification, name="trigger_test_notification"),
    path("qr-registration-notification/", views.qr_registration_notification, name="qr_registration_notification"),
    path("track-notification-event/", views.track_notification_event, name="track_notification_event"),
    path("feedback/", views.feedback_page, name="feedback_page"),
    path("track-page-view/", views.PageVisitors, name="track_page_view"),
    path("token-debug/", views.token_debug_page, name="token_debug"),
    path("firebase-debug/", views.firebase_debug_page, name="firebase_debug"),
    path("domain-test/", views.domain_test_page, name="domain_test"),

    # Register URLs
    path("register/", views.register_page, name="register_page"),
    path("student-register/", views.student_register, name="student_register"),
    path("invoice-register/", views.invoice_register, name="invoice_register"),
    path("all-invoices/", views.library_data, name="all_invoices"),  # Redirect existing table view

    # API endpoints
    path("api/student-invoices/<slug:student_slug>/", views.get_student_invoices, name="get_student_invoices"),

    # Partial Payments
    path("partial-payments/", views.partial_payments_list, name="partial_payments_list"),
    path("mark-invoice-complete/<slug:invoice_slug>/", views.mark_invoice_complete, name="mark_invoice_complete"),
    path("send-payment-reminder/<slug:invoice_slug>/", views.send_payment_reminder, name="send_payment_reminder"),

]
